# 组织管理模块 HTTP Client 测试指南

## 概述

本文档提供了使用 IntelliJ IDEA HTTP Client 测试组织管理模块 API 的完整指南。`organization.http` 文件包含了所有 API 接口的测试用例，涵盖正常场景、错误场景、边界值测试和性能测试。

## 环境配置

### 1. 环境变量设置

在 `organization.http` 文件中配置以下环境变量：

```http
@baseUrl = http://localhost:8080
@token = Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 2. 多环境配置

可以创建 `http-client.env.json` 文件来管理多环境配置：

```json
{
  "development": {
    "baseUrl": "http://localhost:8080",
    "token": "Bearer dev_token_here"
  },
  "test": {
    "baseUrl": "http://test-server:8080",
    "token": "Bearer test_token_here"
  },
  "production": {
    "baseUrl": "https://prod-server.com",
    "token": "Bearer prod_token_here"
  }
}
```

## 测试用例分类

### 1. 机构管理 API (16个测试用例)

#### 基础功能测试
- **查询机构列表**: 支持分页、排序、筛选
- **查询机构详情**: 获取单个机构完整信息
- **创建机构**: 支持学院、部门、非常设机构
- **更新机构**: 基本信息更新、属性更新、状态更新
- **机构划转**: 支持子机构批量划转
- **机构合并**: 支持合并到现有机构或创建新机构

#### 数据导入导出
- **导出功能**: 支持 Excel、PDF 格式
- **导入功能**: 支持 Excel 批量导入
- **架构图**: 获取可视化架构数据

### 2. 编制管理 API (8个测试用例)

#### 编制规划管理
- **查询编制规划**: 支持多维度筛选
- **创建编制规划**: 支持不同岗位类别
- **更新编制规划**: 支持版本管理
- **删除编制规划**: 逻辑删除
- **编制统计**: 实时统计分析

### 3. 人事文件管理 API (6个测试用例)

#### 文件管理
- **查询文件列表**: 支持多条件筛选
- **上传文件**: 支持多种文件类型
- **更新文件信息**: 元数据管理
- **删除文件**: 逻辑删除和物理删除

### 4. 变更日志 API (3个测试用例)

#### 历史记录查询
- **查询变更日志**: 支持时间范围、操作人等筛选
- **详细变更信息**: 包含变更前后对比

### 5. 岗位序列管理 API (7个测试用例)

#### 序列和等级管理
- **岗位序列管理**: 创建和查询岗位序列
- **岗位等级管理**: 创建和查询岗位等级

## 测试场景说明

### 1. 正常业务场景

#### 机构生命周期测试
```http
# 1. 创建机构
POST /api/organizations/institutions/create

# 2. 查询机构详情
GET /api/organizations/institutions/detail

# 3. 更新机构信息
POST /api/organizations/institutions/update

# 4. 机构划转
POST /api/organizations/institutions/transfer

# 5. 停用机构
POST /api/organizations/institutions/updateStatus
```

#### 编制管理流程测试
```http
# 1. 上传人事文件
POST /api/organizations/hr-documents/upload

# 2. 创建编制规划
POST /api/organizations/staffing-plans/create

# 3. 查询编制统计
GET /api/organizations/staffing-stats

# 4. 更新编制规划
POST /api/organizations/staffing-plans/update
```

### 2. 错误场景测试

#### 数据验证错误
- **重复机构编码**: 测试唯一性约束
- **无效机构类型**: 测试枚举值验证
- **循环父子关系**: 测试业务逻辑验证
- **不存在的资源**: 测试404错误处理

#### 权限验证错误
- **无权限访问**: 测试401未授权
- **无效Token**: 测试Token验证
- **过期Token**: 测试Token过期处理

### 3. 边界值测试

#### 数据长度边界
- **最长机构名称**: 测试200字符限制
- **最大编制数**: 测试9999上限
- **最小编制数**: 测试0下限
- **最大文件大小**: 测试100MB限制

### 4. 性能测试

#### 大数据量查询
- **大量数据查询**: 1000条记录分页查询
- **复杂条件查询**: 多条件组合查询
- **关联数据查询**: 包含子表数据的查询

### 5. 并发测试

#### 并发操作测试
- **同时创建机构**: 测试并发创建处理
- **同时更新机构**: 测试乐观锁机制
- **资源竞争**: 测试数据一致性

## 使用方法

### 1. 在 IntelliJ IDEA 中使用

1. 打开 `organization.http` 文件
2. 点击请求左侧的绿色箭头执行单个请求
3. 使用 `Ctrl+Enter` 快捷键执行当前请求
4. 查看响应结果在底部面板

### 2. 批量执行测试

```bash
# 使用命令行工具执行所有测试
curl -X GET "http://localhost:8080/api/organizations/institutions" \
  -H "Authorization: Bearer your_token_here" \
  -H "Content-Type: application/json"
```

### 3. 测试数据准备

#### 基础测试数据
```sql
-- 插入根机构
INSERT INTO org_institution (institution_id, institution_name, institution_code, institution_type, is_ad_hoc, status) 
VALUES ('inst_root_001', '杭州科技职业技术学院', 'ROOT_001', 'COLLEGE', false, 'NORMAL');

-- 插入测试用户
INSERT INTO sys_user (user_id, username, nickname) 
VALUES ('user_001', 'test_admin', '测试管理员');

-- 插入岗位序列
INSERT INTO org_position_sequence (sequence_id, sequence_name, sequence_code, status) 
VALUES ('seq_001', '管理岗', 'MGMT', 'NORMAL');
```

## 测试检查点

### 1. 响应格式验证

所有API响应应符合统一格式：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 具体业务数据
  }
}
```

### 2. 数据完整性检查

- **机构创建**: 检查返回的机构ID是否有效
- **关联关系**: 检查父子机构关系是否正确
- **历史记录**: 检查变更日志是否正确记录
- **编制统计**: 检查统计数据是否准确

### 3. 业务规则验证

- **唯一性约束**: 机构编码、序列编码等
- **状态转换**: 机构状态变更规则
- **权限控制**: API访问权限验证
- **数据固化**: 历史数据固化机制

## 常见问题

### 1. Token 过期

**问题**: 请求返回401未授权错误
**解决**: 更新 `@token` 变量中的JWT Token

### 2. 数据不存在

**问题**: 查询返回404错误
**解决**: 检查测试数据是否已正确初始化

### 3. 权限不足

**问题**: 请求返回403禁止访问错误
**解决**: 确认当前用户具有相应的操作权限

### 4. 参数验证失败

**问题**: 请求返回400参数错误
**解决**: 检查请求参数是否符合DTO验证规则

## 测试报告

### 执行测试后检查以下指标：

1. **功能覆盖率**: 所有API接口是否都能正常调用
2. **错误处理**: 异常场景是否返回正确的错误码和信息
3. **性能指标**: 响应时间是否在可接受范围内
4. **数据一致性**: 操作后数据状态是否正确
5. **日志记录**: 重要操作是否正确记录日志

### 测试结果记录模板：

```
测试日期: 2025-08-07
测试环境: 开发环境
测试人员: 张三

测试结果:
- 机构管理API: 16/16 通过
- 编制管理API: 8/8 通过  
- 人事文件API: 6/6 通过
- 变更日志API: 3/3 通过
- 岗位序列API: 7/7 通过
- 错误场景测试: 5/5 通过
- 边界值测试: 4/4 通过
- 性能测试: 2/2 通过

总计: 51/51 通过
```

## 扩展测试

### 1. 自动化测试集成

可以将HTTP请求转换为自动化测试脚本：

```javascript
// 示例：使用Newman运行Postman集合
newman run organization-api-tests.json \
  --environment test-env.json \
  --reporters cli,html \
  --reporter-html-export test-report.html
```

### 2. 压力测试

使用JMeter或其他工具进行压力测试：

```xml
<!-- JMeter测试计划示例 -->
<TestPlan>
  <ThreadGroup>
    <HTTPSamplerProxy>
      <stringProp name="HTTPSampler.domain">localhost</stringProp>
      <stringProp name="HTTPSampler.port">8080</stringProp>
      <stringProp name="HTTPSampler.path">/api/organizations/institutions</stringProp>
    </HTTPSamplerProxy>
  </ThreadGroup>
</TestPlan>
```

### 3. 监控集成

结合监控工具观察API性能：

```yaml
# Prometheus监控配置示例
- job_name: 'organization-api'
  static_configs:
    - targets: ['localhost:8080']
  metrics_path: '/actuator/prometheus'
```
