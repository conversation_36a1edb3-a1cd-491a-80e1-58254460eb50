### 组织管理模块 API 测试文件
### 基于IntelliJ IDEA HTTP Client格式

### 环境变量配置
@baseUrl = http://localhost:8080
@token = Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

### ==================== 机构管理 API ====================

### 1. 查询机构列表 - 基础查询
GET {{baseUrl}}/api/organizations/institutions
Authorization: {{token}}
Content-Type: application/json

### 预期响应示例 - 查询机构列表（基础查询）
### {
###   "code": 200,
###   "message": "查询成功",
###   "data": {
###     "content": [
###       {
###         "institutionId": "inst_001",
###         "institutionName": "计算机学院",
###         "institutionCode": "CS001",
###         "institutionType": "COLLEGE",
###         "isAdHoc": false,
###         "parentInstitutionId": "inst_root_001",
###         "parentInstitutionName": "杭州科技职业技术学院",
###         "status": "NORMAL",
###         "leaderId": "emp_001",
###         "leaderName": "张三",
###         "contactPhone": "0571-88888888",
###         "officeAddress": "教学楼A座301室",
###         "level": 2,
###         "sortOrder": 1,
###         "createTime": "2025-01-01T10:00:00",
###         "updateTime": "2025-01-15T14:30:00",
###         "staffingStats": {
###           "approvedCount": 50,
###           "currentCount": 45,
###           "overCount": 0,
###           "shortageCount": 5,
###           "utilizationRate": 90.0
###         }
###       },
###       {
###         "institutionId": "inst_002",
###         "institutionName": "机械工程学院",
###         "institutionCode": "ME001",
###         "institutionType": "COLLEGE",
###         "isAdHoc": false,
###         "parentInstitutionId": "inst_root_001",
###         "parentInstitutionName": "杭州科技职业技术学院",
###         "status": "NORMAL",
###         "leaderId": "emp_002",
###         "leaderName": "李四",
###         "contactPhone": "0571-88888889",
###         "officeAddress": "教学楼B座201室",
###         "level": 2,
###         "sortOrder": 2,
###         "createTime": "2025-01-02T09:00:00",
###         "updateTime": "2025-01-10T16:20:00",
###         "staffingStats": {
###           "approvedCount": 40,
###           "currentCount": 42,
###           "overCount": 2,
###           "shortageCount": 0,
###           "utilizationRate": 105.0
###         }
###       }
###     ],
###     "pageable": {
###       "sort": {
###         "sorted": true,
###         "unsorted": false
###       },
###       "pageNumber": 0,
###       "pageSize": 20,
###       "offset": 0,
###       "paged": true,
###       "unpaged": false
###     },
###     "totalElements": 15,
###     "totalPages": 1,
###     "last": true,
###     "first": true,
###     "numberOfElements": 15,
###     "size": 20,
###     "number": 0,
###     "sort": {
###       "sorted": true,
###       "unsorted": false
###     }
###   }
### }

### 2. 查询机构列表 - 带分页和筛选条件
GET {{baseUrl}}/api/organizations/institutions?page=0&size=10&sort=createTime,desc&institutionName=计算机&institutionType=COLLEGE&status=NORMAL
Authorization: {{token}}
Content-Type: application/json

### 预期响应示例 - 查询机构列表（带筛选条件）
### {
###   "code": 200,
###   "message": "查询成功",
###   "data": {
###     "content": [
###       {
###         "institutionId": "inst_001",
###         "institutionName": "计算机学院",
###         "institutionCode": "CS001",
###         "institutionType": "COLLEGE",
###         "isAdHoc": false,
###         "parentInstitutionId": "inst_root_001",
###         "parentInstitutionName": "杭州科技职业技术学院",
###         "status": "NORMAL",
###         "leaderId": "emp_001",
###         "leaderName": "张三",
###         "contactPhone": "0571-88888888",
###         "officeAddress": "教学楼A座301室",
###         "level": 2,
###         "sortOrder": 1,
###         "createTime": "2025-01-15T10:00:00",
###         "updateTime": "2025-01-15T14:30:00",
###         "staffingStats": {
###           "approvedCount": 50,
###           "currentCount": 45,
###           "overCount": 0,
###           "shortageCount": 5,
###           "utilizationRate": 90.0
###         }
###       },
###       {
###         "institutionId": "inst_003",
###         "institutionName": "计算机科学与技术系",
###         "institutionCode": "CS002",
###         "institutionType": "DEPARTMENT",
###         "isAdHoc": false,
###         "parentInstitutionId": "inst_001",
###         "parentInstitutionName": "计算机学院",
###         "status": "NORMAL",
###         "leaderId": "emp_003",
###         "leaderName": "王五",
###         "contactPhone": "0571-88888890",
###         "officeAddress": "教学楼A座302室",
###         "level": 3,
###         "sortOrder": 1,
###         "createTime": "2025-01-10T09:30:00",
###         "updateTime": "2025-01-12T11:15:00",
###         "staffingStats": {
###           "approvedCount": 25,
###           "currentCount": 23,
###           "overCount": 0,
###           "shortageCount": 2,
###           "utilizationRate": 92.0
###         }
###       }
###     ],
###     "pageable": {
###       "sort": {
###         "sorted": true,
###         "unsorted": false
###       },
###       "pageNumber": 0,
###       "pageSize": 10,
###       "offset": 0,
###       "paged": true,
###       "unpaged": false
###     },
###     "totalElements": 2,
###     "totalPages": 1,
###     "last": true,
###     "first": true,
###     "numberOfElements": 2,
###     "size": 10,
###     "number": 0,
###     "sort": {
###       "sorted": true,
###       "unsorted": false
###     }
###   }
### }

### 3. 查询机构详情
GET {{baseUrl}}/api/organizations/institutions/detail?institutionId=inst_001
Authorization: {{token}}
Content-Type: application/json

### 预期响应示例 - 查询机构详情
### {
###   "code": 200,
###   "message": "查询成功",
###   "data": {
###     "institutionId": "inst_001",
###     "institutionName": "计算机学院",
###     "institutionCode": "CS001",
###     "institutionType": "COLLEGE",
###     "isAdHoc": false,
###     "parentInstitutionId": "inst_root_001",
###     "parentInstitutionName": "杭州科技职业技术学院",
###     "status": "NORMAL",
###     "reasonForChange": "根据学校发展需要设立",
###     "solidifiedDataDescription": null,
###     "responsibilities": "负责计算机相关专业的教学、科研和人才培养工作",
###     "attachmentUrls": "http://oss.example.com/cs_intro.pdf,http://oss.example.com/cs_plan.pdf",
###     "level": 2,
###     "leaderId": "emp_001",
###     "leaderName": "张三",
###     "contactPhone": "0571-88888888",
###     "officeAddress": "教学楼A座301室",
###     "sortOrder": 1,
###     "createTime": "2025-01-01T10:00:00",
###     "updateTime": "2025-01-15T14:30:00",
###     "createBy": "user_admin_001",
###     "updateBy": "user_admin_001",
###     "children": [
###       {
###         "institutionId": "inst_003",
###         "institutionName": "计算机科学与技术系",
###         "institutionCode": "CS002",
###         "institutionType": "DEPARTMENT",
###         "isAdHoc": false,
###         "status": "NORMAL",
###         "level": 3,
###         "sortOrder": 1,
###         "staffingStats": {
###           "approvedCount": 25,
###           "currentCount": 23,
###           "overCount": 0,
###           "shortageCount": 2,
###           "utilizationRate": 92.0
###         }
###       },
###       {
###         "institutionId": "inst_004",
###         "institutionName": "软件工程系",
###         "institutionCode": "SE001",
###         "institutionType": "DEPARTMENT",
###         "isAdHoc": false,
###         "status": "NORMAL",
###         "level": 3,
###         "sortOrder": 2,
###         "staffingStats": {
###           "approvedCount": 20,
###           "currentCount": 18,
###           "overCount": 0,
###           "shortageCount": 2,
###           "utilizationRate": 90.0
###         }
###       }
###     ],
###     "staffingStats": {
###       "approvedCount": 50,
###       "currentCount": 45,
###       "overCount": 0,
###       "shortageCount": 5,
###       "utilizationRate": 90.0
###     }
###   }
### }

### 4. 创建机构 - 学院
POST {{baseUrl}}/api/organizations/institutions/create
Authorization: {{token}}
Content-Type: application/json

{
  "institutionName": "人工智能学院",
  "institutionCode": "AI001",
  "institutionType": "COLLEGE",
  "isAdHoc": false,
  "parentInstitutionId": "inst_root_001",
  "responsibilities": "负责人工智能相关专业的教学、科研和人才培养工作",
  "leaderId": "emp_001",
  "contactPhone": "0571-88888888",
  "officeAddress": "教学楼A座301室",
  "sortOrder": 1,
  "reasonForChange": "根据学校发展需要新设人工智能学院",
  "approvalDocNumber": "杭科院〔2025〕001号",
  "hrDocumentId": "doc_001"
}

### 5. 创建机构 - 非常设机构
POST {{baseUrl}}/api/organizations/institutions/create
Authorization: {{token}}
Content-Type: application/json

{
  "institutionName": "疫情防控工作组",
  "institutionCode": "COVID_001",
  "institutionType": "AD_HOC",
  "isAdHoc": true,
  "parentInstitutionId": "inst_admin_001",
  "responsibilities": "负责学校疫情防控相关工作的统筹协调",
  "attachmentUrls": "http://oss.example.com/covid_plan.pdf,http://oss.example.com/covid_guide.pdf",
  "leaderId": "emp_002",
  "contactPhone": "0571-88888889",
  "officeAddress": "行政楼临时办公室",
  "sortOrder": 99,
  "reasonForChange": "根据疫情防控需要临时设立工作组",
  "approvalDocNumber": "杭科院〔2025〕002号"
}

### 6. 更新机构信息
POST {{baseUrl}}/api/organizations/institutions/update
Authorization: {{token}}
Content-Type: application/json

{
  "institutionId": "inst_001",
  "institutionName": "计算机与人工智能学院",
  "institutionCode": "CS_AI_001",
  "institutionType": "COLLEGE",
  "responsibilities": "负责计算机科学与技术、人工智能等相关专业的教学科研工作",
  "reasonForChange": "根据学科发展需要调整学院名称和职责范围",
  "approvalDocNumber": "杭科院〔2025〕003号",
  "hrDocumentId": "doc_002"
}

### 7. 更新部门属性
POST {{baseUrl}}/api/organizations/institutions/updateProperties
Authorization: {{token}}
Content-Type: application/json

{
  "institutionId": "inst_001",
  "institutionType": "COLLEGE",
  "level": 2,
  "parentInstitutionId": "inst_root_001",
  "leaderId": "emp_003",
  "contactPhone": "0571-88888890",
  "officeAddress": "教学楼A座401室",
  "sortOrder": 2,
  "reasonForChange": "根据人事调整更新部门负责人和联系方式",
  "approvalDocNumber": "杭科院〔2025〕004号"
}

### 8. 修改机构状态 - 停用
POST {{baseUrl}}/api/organizations/institutions/updateStatus
Authorization: {{token}}
Content-Type: application/json

{
  "institutionId": "inst_002",
  "status": "DISABLED",
  "reasonForChange": "根据学校组织架构调整，暂时停用该机构",
  "approvalDocNumber": "杭科院〔2025〕005号",
  "hrDocumentId": "doc_003"
}

### 9. 修改机构状态 - 启用
POST {{baseUrl}}/api/organizations/institutions/updateStatus
Authorization: {{token}}
Content-Type: application/json

{
  "institutionId": "inst_002",
  "status": "NORMAL",
  "reasonForChange": "根据工作需要重新启用该机构",
  "approvalDocNumber": "杭科院〔2025〕006号"
}

### 10. 机构划转
POST {{baseUrl}}/api/organizations/institutions/transfer
Authorization: {{token}}
Content-Type: application/json

{
  "institutionId": "inst_003",
  "newParentInstitutionId": "inst_parent_002",
  "reason": "根据学校管理体制调整，将该机构划转到新的上级机构",
  "approvalDocNumber": "杭科院〔2025〕007号",
  "hrDocumentId": "doc_004",
  "includeChildren": true,
  "updateEmployees": true
}

### 11. 机构合并 - 合并到现有机构
POST {{baseUrl}}/api/organizations/institutions/merge
Authorization: {{token}}
Content-Type: application/json

{
  "sourceInstitutionIds": ["inst_004", "inst_005"],
  "targetInstitutionId": "inst_006",
  "mergeReason": "根据学科建设需要，整合相关机构资源",
  "approvalDocNumber": "杭科院〔2025〕008号",
  "hrDocumentId": "doc_005",
  "mergeStrategy": "MERGE_TO_EXISTING",
  "preserveHistory": true,
  "employeeHandlingStrategy": "AUTO_TRANSFER"
}

### 12. 机构合并 - 创建新机构
POST {{baseUrl}}/api/organizations/institutions/merge
Authorization: {{token}}
Content-Type: application/json

{
  "sourceInstitutionIds": ["inst_007", "inst_008"],
  "targetInstitutionId": "",
  "mergeReason": "整合资源，成立新的综合性学院",
  "approvalDocNumber": "杭科院〔2025〕009号",
  "hrDocumentId": "doc_006",
  "mergeStrategy": "CREATE_NEW",
  "newInstitutionInfo": {
    "institutionName": "综合管理学院",
    "institutionCode": "GM001",
    "institutionType": "COLLEGE",
    "parentInstitutionId": "inst_root_001",
    "responsibilities": "负责综合管理相关专业的教学科研工作",
    "leaderId": "emp_004",
    "contactPhone": "0571-88888891",
    "officeAddress": "教学楼B座201室"
  },
  "preserveHistory": true,
  "employeeHandlingStrategy": "AUTO_TRANSFER"
}

### 13. 导出机构数据 - Excel格式
GET {{baseUrl}}/api/organizations/institutions/export?format=excel&institutionType=COLLEGE&status=NORMAL
Authorization: {{token}}
Content-Type: application/json

### 预期响应示例 - 导出机构数据（Excel格式）
### {
###   "code": 200,
###   "message": "导出成功",
###   "data": {
###     "exportId": "export_20250807_001",
###     "fileName": "机构列表_20250807_143022.xlsx",
###     "fileUrl": "http://oss.example.com/exports/institutions_20250807_143022.xlsx",
###     "fileSize": 2048576,
###     "exportTime": "2025-08-07T14:30:22",
###     "totalRecords": 15,
###     "exportedRecords": 15,
###     "exportStatus": "SUCCESS",
###     "downloadExpireTime": "2025-08-14T14:30:22",
###     "exportParams": {
###       "format": "excel",
###       "institutionType": "COLLEGE",
###       "status": "NORMAL",
###       "includeChildren": false,
###       "includeStaffingStats": true
###     }
###   }
### }

### 14. 导出机构数据 - PDF格式
GET {{baseUrl}}/api/organizations/institutions/export?format=pdf&parentInstitutionId=inst_root_001
Authorization: {{token}}
Content-Type: application/json

### 预期响应示例 - 导出机构数据（PDF格式）
### {
###   "code": 200,
###   "message": "导出成功",
###   "data": {
###     "exportId": "export_20250807_002",
###     "fileName": "组织架构图_20250807_143045.pdf",
###     "fileUrl": "http://oss.example.com/exports/org_chart_20250807_143045.pdf",
###     "fileSize": 5242880,
###     "exportTime": "2025-08-07T14:30:45",
###     "totalRecords": 25,
###     "exportedRecords": 25,
###     "exportStatus": "SUCCESS",
###     "downloadExpireTime": "2025-08-14T14:30:45",
###     "exportParams": {
###       "format": "pdf",
###       "parentInstitutionId": "inst_root_001",
###       "includeChildren": true,
###       "chartType": "tree"
###     }
###   }
### }

### 15. 导入机构数据
POST {{baseUrl}}/api/organizations/institutions/import
Authorization: {{token}}
Content-Type: multipart/form-data

--boundary
Content-Disposition: form-data; name="file"; filename="institutions.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

< ./test-data/institutions.xlsx
--boundary
Content-Disposition: form-data; name="importParams"

{"validateOnly": false, "skipDuplicates": true}
--boundary--

### 16. 获取架构图数据
GET {{baseUrl}}/api/organizations/institutions/diagram?rootInstitutionId=inst_root_001&includeChildren=true&includeStaffingStats=true
Authorization: {{token}}
Content-Type: application/json

### 预期响应示例 - 获取架构图数据
### {
###   "code": 200,
###   "message": "查询成功",
###   "data": {
###     "rootNode": {
###       "id": "inst_root_001",
###       "name": "杭州科技职业技术学院",
###       "code": "ROOT_001",
###       "type": "COLLEGE",
###       "level": 1,
###       "status": "NORMAL",
###       "leader": "校长",
###       "staffingStats": {
###         "approvedCount": 500,
###         "currentCount": 485,
###         "utilizationRate": 97.0
###       },
###       "position": {
###         "x": 500,
###         "y": 50
###       }
###     },
###     "nodes": [
###       {
###         "id": "inst_001",
###         "name": "计算机学院",
###         "code": "CS001",
###         "type": "COLLEGE",
###         "level": 2,
###         "status": "NORMAL",
###         "parentId": "inst_root_001",
###         "leader": "张三",
###         "staffingStats": {
###           "approvedCount": 50,
###           "currentCount": 45,
###           "utilizationRate": 90.0
###         },
###         "position": {
###           "x": 200,
###           "y": 150
###         }
###       },
###       {
###         "id": "inst_002",
###         "name": "机械工程学院",
###         "code": "ME001",
###         "type": "COLLEGE",
###         "level": 2,
###         "status": "NORMAL",
###         "parentId": "inst_root_001",
###         "leader": "李四",
###         "staffingStats": {
###           "approvedCount": 40,
###           "currentCount": 42,
###           "utilizationRate": 105.0
###         },
###         "position": {
###           "x": 500,
###           "y": 150
###         }
###       },
###       {
###         "id": "inst_005",
###         "name": "行政管理部",
###         "code": "ADMIN001",
###         "type": "DEPARTMENT",
###         "level": 2,
###         "status": "NORMAL",
###         "parentId": "inst_root_001",
###         "leader": "赵六",
###         "staffingStats": {
###           "approvedCount": 30,
###           "currentCount": 28,
###           "utilizationRate": 93.3
###         },
###         "position": {
###           "x": 800,
###           "y": 150
###         }
###       }
###     ],
###     "edges": [
###       {
###         "id": "edge_001",
###         "source": "inst_root_001",
###         "target": "inst_001",
###         "type": "hierarchy"
###       },
###       {
###         "id": "edge_002",
###         "source": "inst_root_001",
###         "target": "inst_002",
###         "type": "hierarchy"
###       },
###       {
###         "id": "edge_003",
###         "source": "inst_root_001",
###         "target": "inst_005",
###         "type": "hierarchy"
###       }
###     ],
###     "metadata": {
###       "totalNodes": 4,
###       "totalLevels": 2,
###       "generatedTime": "2025-08-07T14:35:00",
###       "chartType": "tree",
###       "layout": "hierarchical"
###     }
###   }
### }

### ==================== 编制管理 API ====================

### 17. 查询编制规划列表
GET {{baseUrl}}/api/organizations/staffing-plans?page=0&size=20&planYear=2025&institutionId=inst_001&positionCategory=管理岗
Authorization: {{token}}
Content-Type: application/json

### 预期响应示例 - 查询编制规划列表
### {
###   "code": 200,
###   "message": "查询成功",
###   "data": {
###     "content": [
###       {
###         "planId": "plan_001",
###         "planYear": 2025,
###         "institutionId": "inst_001",
###         "institutionName": "计算机学院",
###         "positionCategory": "管理岗",
###         "approvedCount": 15,
###         "effectiveDate": "2025-01-01",
###         "endDate": "2025-12-31",
###         "hrDocumentId": "doc_001",
###         "hrDocumentName": "2025年编制规划文件",
###         "approvalStatus": "已批准",
###         "currentCount": 12,
###         "overCount": 0,
###         "shortageCount": 3,
###         "utilizationRate": 80.0,
###         "createTime": "2024-12-15T10:00:00",
###         "updateTime": "2025-01-05T14:30:00",
###         "createBy": "user_admin_001",
###         "createByName": "系统管理员",
###         "updateBy": "user_hr_001",
###         "updateByName": "人事专员",
###         "remarks": "根据学校发展需要制定的2025年度管理岗编制规划"
###       },
###       {
###         "planId": "plan_002",
###         "planYear": 2025,
###         "institutionId": "inst_001",
###         "institutionName": "计算机学院",
###         "positionCategory": "专技岗",
###         "approvedCount": 35,
###         "effectiveDate": "2025-01-01",
###         "endDate": "2025-12-31",
###         "hrDocumentId": "doc_001",
###         "hrDocumentName": "2025年编制规划文件",
###         "approvalStatus": "已批准",
###         "currentCount": 33,
###         "overCount": 0,
###         "shortageCount": 2,
###         "utilizationRate": 94.3,
###         "createTime": "2024-12-15T10:30:00",
###         "updateTime": "2025-01-05T14:45:00",
###         "createBy": "user_admin_001",
###         "createByName": "系统管理员",
###         "updateBy": "user_hr_001",
###         "updateByName": "人事专员",
###         "remarks": "根据教学需要制定的2025年度专技岗编制规划"
###       }
###     ],
###     "pageable": {
###       "sort": {
###         "sorted": true,
###         "unsorted": false
###       },
###       "pageNumber": 0,
###       "pageSize": 20,
###       "offset": 0,
###       "paged": true,
###       "unpaged": false
###     },
###     "totalElements": 2,
###     "totalPages": 1,
###     "last": true,
###     "first": true,
###     "numberOfElements": 2,
###     "size": 20,
###     "number": 0
###   }
### }

### 18. 查询编制规划详情
GET {{baseUrl}}/api/organizations/staffing-plans/detail?planId=plan_001
Authorization: {{token}}
Content-Type: application/json

### 预期响应示例 - 查询编制规划详情
### {
###   "code": 200,
###   "message": "查询成功",
###   "data": {
###     "planId": "plan_001",
###     "planYear": 2025,
###     "institutionId": "inst_001",
###     "institutionName": "计算机学院",
###     "positionCategory": "管理岗",
###     "approvedCount": 15,
###     "effectiveDate": "2025-01-01",
###     "endDate": "2025-12-31",
###     "hrDocumentId": "doc_001",
###     "hrDocumentName": "2025年度编制规划文件",
###     "approvalStatus": "已批准",
###     "currentCount": 12,
###     "overCount": 0,
###     "shortageCount": 3,
###     "utilizationRate": 80.0,
###     "createTime": "2024-12-15T10:00:00",
###     "updateTime": "2025-01-05T14:30:00",
###     "createBy": "user_admin_001",
###     "createByName": "系统管理员",
###     "updateBy": "user_hr_001",
###     "updateByName": "人事专员",
###     "remarks": "根据学校发展需要制定的2025年度管理岗编制规划",
###     "historyVersions": [
###       {
###         "planId": "plan_001_v1",
###         "approvedCount": 12,
###         "effectiveDate": "2024-01-01",
###         "endDate": "2024-12-31",
###         "approvalStatus": "已批准",
###         "createTime": "2023-12-10T09:00:00",
###         "remarks": "2024年度管理岗编制规划"
###       }
###     ],
###     "relatedDocuments": [
###       {
###         "documentId": "doc_001",
###         "documentName": "2025年度编制规划文件",
###         "documentType": "STAFFING_PLAN",
###         "effectiveDate": "2025-01-01"
###       }
###     ]
###   }
### }

### 19. 创建编制规划
POST {{baseUrl}}/api/organizations/staffing-plans/create
Authorization: {{token}}
Content-Type: application/json

{
  "planYear": 2025,
  "institutionId": "inst_001",
  "positionCategory": "管理岗",
  "approvedCount": 50,
  "effectiveDate": "2025-01-01",
  "endDate": "2025-12-31",
  "hrDocumentId": "doc_007",
  "approvalStatus": "草稿",
  "remarks": "根据学校发展需要制定的2025年度编制规划"
}

### 20. 更新编制规划 - 创建新版本
POST {{baseUrl}}/api/organizations/staffing-plans/update
Authorization: {{token}}
Content-Type: application/json

{
  "planId": "plan_001",
  "approvedCount": 55,
  "effectiveDate": "2025-06-01",
  "hrDocumentId": "doc_008",
  "approvalStatus": "已批准",
  "remarks": "根据中期调整增加编制数量",
  "createNewVersion": true
}

### 21. 更新编制规划 - 更新当前版本
POST {{baseUrl}}/api/organizations/staffing-plans/update
Authorization: {{token}}
Content-Type: application/json

{
  "planId": "plan_001",
  "approvalStatus": "已批准",
  "remarks": "审批通过",
  "createNewVersion": false
}

### 22. 删除编制规划
POST {{baseUrl}}/api/organizations/staffing-plans/delete
Authorization: {{token}}
Content-Type: application/json

{
  "planId": "plan_002",
  "deleteReason": "规划调整，不再需要此编制规划"
}

### 23. 查询机构编制统计 - 全部统计
GET {{baseUrl}}/api/organizations/staffing-stats
Authorization: {{token}}
Content-Type: application/json

### 预期响应示例 - 查询机构编制统计（全部统计）
### {
###   "code": 200,
###   "message": "查询成功",
###   "data": {
###     "summary": {
###       "totalInstitutions": 15,
###       "totalApprovedCount": 500,
###       "totalCurrentCount": 485,
###       "totalOverCount": 5,
###       "totalShortageCount": 20,
###       "overallUtilizationRate": 97.0,
###       "statisticsDate": "2025-08-07T15:00:00"
###     },
###     "byInstitution": [
###       {
###         "institutionId": "inst_001",
###         "institutionName": "计算机学院",
###         "institutionType": "COLLEGE",
###         "totalApprovedCount": 50,
###         "totalCurrentCount": 45,
###         "totalOverCount": 0,
###         "totalShortageCount": 5,
###         "utilizationRate": 90.0,
###         "byCategory": [
###           {
###             "positionCategory": "管理岗",
###             "approvedCount": 15,
###             "currentCount": 12,
###             "overCount": 0,
###             "shortageCount": 3,
###             "utilizationRate": 80.0
###           },
###           {
###             "positionCategory": "专技岗",
###             "approvedCount": 35,
###             "currentCount": 33,
###             "overCount": 0,
###             "shortageCount": 2,
###             "utilizationRate": 94.3
###           }
###         ]
###       },
###       {
###         "institutionId": "inst_002",
###         "institutionName": "机械工程学院",
###         "institutionType": "COLLEGE",
###         "totalApprovedCount": 40,
###         "totalCurrentCount": 42,
###         "totalOverCount": 2,
###         "totalShortageCount": 0,
###         "utilizationRate": 105.0,
###         "byCategory": [
###           {
###             "positionCategory": "管理岗",
###             "approvedCount": 12,
###             "currentCount": 13,
###             "overCount": 1,
###             "shortageCount": 0,
###             "utilizationRate": 108.3
###           },
###           {
###             "positionCategory": "专技岗",
###             "approvedCount": 28,
###             "currentCount": 29,
###             "overCount": 1,
###             "shortageCount": 0,
###             "utilizationRate": 103.6
###           }
###         ]
###       }
###     ],
###     "byCategory": [
###       {
###         "positionCategory": "管理岗",
###         "totalApprovedCount": 150,
###         "totalCurrentCount": 142,
###         "totalOverCount": 2,
###         "totalShortageCount": 10,
###         "utilizationRate": 94.7
###       },
###       {
###         "positionCategory": "专技岗",
###         "totalApprovedCount": 300,
###         "totalCurrentCount": 295,
###         "totalOverCount": 3,
###         "totalShortageCount": 8,
###         "utilizationRate": 98.3
###       },
###       {
###         "positionCategory": "工勤岗",
###         "totalApprovedCount": 50,
###         "totalCurrentCount": 48,
###         "totalOverCount": 0,
###         "totalShortageCount": 2,
###         "utilizationRate": 96.0
###       }
###     ]
###   }
### }

### 24. 查询机构编制统计 - 指定机构和年度
GET {{baseUrl}}/api/organizations/staffing-stats?institutionId=inst_001&year=2025&positionCategory=管理岗
Authorization: {{token}}
Content-Type: application/json

### 预期响应示例 - 查询机构编制统计（指定条件）
### {
###   "code": 200,
###   "message": "查询成功",
###   "data": {
###     "institutionId": "inst_001",
###     "institutionName": "计算机学院",
###     "year": 2025,
###     "positionCategory": "管理岗",
###     "approvedCount": 15,
###     "currentCount": 12,
###     "overCount": 0,
###     "shortageCount": 3,
###     "utilizationRate": 80.0,
###     "statisticsDate": "2025-08-07T15:00:00",
###     "monthlyTrend": [
###       {
###         "month": "2025-01",
###         "currentCount": 10,
###         "utilizationRate": 66.7
###       },
###       {
###         "month": "2025-02",
###         "currentCount": 11,
###         "utilizationRate": 73.3
###       },
###       {
###         "month": "2025-03",
###         "currentCount": 12,
###         "utilizationRate": 80.0
###       }
###     ],
###     "positionLevelBreakdown": [
###       {
###         "levelName": "一级",
###         "approvedCount": 3,
###         "currentCount": 2,
###         "utilizationRate": 66.7
###       },
###       {
###         "levelName": "二级",
###         "approvedCount": 5,
###         "currentCount": 4,
###         "utilizationRate": 80.0
###       },
###       {
###         "levelName": "三级",
###         "approvedCount": 7,
###         "currentCount": 6,
###         "utilizationRate": 85.7
###       }
###     ]
###   }
### }

### ==================== 人事文件管理 API ====================

### 25. 查询人事文件列表
GET {{baseUrl}}/api/organizations/hr-documents?page=0&size=20&documentYear=2025&documentType=STAFFING_PLAN&approvalStatus=已批准
Authorization: {{token}}
Content-Type: application/json

### 预期响应示例 - 查询人事文件列表
### {
###   "code": 200,
###   "message": "查询成功",
###   "data": {
###     "content": [
###       {
###         "documentId": "doc_001",
###         "documentName": "2025年度编制规划文件",
###         "documentYear": 2025,
###         "documentType": "STAFFING_PLAN",
###         "documentTypeName": "编制文件",
###         "documentUrl": "http://oss.example.com/documents/2025/staffing_plan_2025.pdf",
###         "fileSize": 2048000,
###         "fileExtension": "pdf",
###         "effectiveDate": "2025-01-01",
###         "approvalStatus": "已批准",
###         "description": "2025年度全校编制规划文件，包含各学院编制分配方案",
###         "keywords": "编制,规划,2025,学院",
###         "uploadTime": "2024-12-10T10:00:00",
###         "uploadBy": "user_admin_001",
###         "uploadByName": "系统管理员",
###         "updateTime": "2024-12-15T14:30:00",
###         "updateBy": "user_hr_001",
###         "updateByName": "人事专员",
###         "downloadCount": 25,
###         "isPublic": false,
###         "relatedStaffingPlanCount": 15,
###         "relatedInstitutionChangeCount": 0
###       },
###       {
###         "documentId": "doc_002",
###         "documentName": "2025年机构调整通知",
###         "documentYear": 2025,
###         "documentType": "INSTITUTION_CHANGE",
###         "documentTypeName": "机构变更文件",
###         "documentUrl": "http://oss.example.com/documents/2025/institution_change_notice.pdf",
###         "fileSize": 1024000,
###         "fileExtension": "pdf",
###         "effectiveDate": "2025-03-01",
###         "approvalStatus": "已批准",
###         "description": "关于调整部分机构设置的通知文件",
###         "keywords": "机构调整,组织架构,2025",
###         "uploadTime": "2025-02-20T09:30:00",
###         "uploadBy": "user_admin_001",
###         "uploadByName": "系统管理员",
###         "updateTime": "2025-02-25T16:45:00",
###         "updateBy": "user_admin_001",
###         "updateByName": "系统管理员",
###         "downloadCount": 18,
###         "isPublic": true,
###         "relatedStaffingPlanCount": 0,
###         "relatedInstitutionChangeCount": 5
###       }
###     ],
###     "pageable": {
###       "sort": {
###         "sorted": true,
###         "unsorted": false
###       },
###       "pageNumber": 0,
###       "pageSize": 20,
###       "offset": 0,
###       "paged": true,
###       "unpaged": false
###     },
###     "totalElements": 2,
###     "totalPages": 1,
###     "last": true,
###     "first": true,
###     "numberOfElements": 2,
###     "size": 20,
###     "number": 0
###   }
### }

### 26. 查询人事文件详情
GET {{baseUrl}}/api/organizations/hr-documents/detail?documentId=doc_001
Authorization: {{token}}
Content-Type: application/json

### 预期响应示例 - 查询人事文件详情
### {
###   "code": 200,
###   "message": "查询成功",
###   "data": {
###     "documentId": "doc_001",
###     "documentName": "2025年度编制规划文件",
###     "documentYear": 2025,
###     "documentType": "STAFFING_PLAN",
###     "documentTypeName": "编制文件",
###     "documentUrl": "http://oss.example.com/documents/2025/staffing_plan_2025.pdf",
###     "fileSize": 2048000,
###     "fileExtension": "pdf",
###     "effectiveDate": "2025-01-01",
###     "approvalStatus": "已批准",
###     "description": "2025年度全校编制规划文件，包含各学院编制分配方案。本文件根据学校发展规划和人才队伍建设需要，对各学院、部门的编制进行了科学合理的配置。",
###     "keywords": "编制,规划,2025,学院,人才队伍",
###     "uploadTime": "2024-12-10T10:00:00",
###     "uploadBy": "user_admin_001",
###     "uploadByName": "系统管理员",
###     "updateTime": "2024-12-15T14:30:00",
###     "updateBy": "user_hr_001",
###     "updateByName": "人事专员",
###     "downloadCount": 25,
###     "isPublic": false,
###     "relatedStaffingPlanCount": 15,
###     "relatedInstitutionChangeCount": 0,
###     "relatedStaffingPlans": [
###       {
###         "planId": "plan_001",
###         "institutionName": "计算机学院",
###         "positionCategory": "管理岗",
###         "approvedCount": 15,
###         "effectiveDate": "2025-01-01"
###       },
###       {
###         "planId": "plan_002",
###         "institutionName": "计算机学院",
###         "positionCategory": "专技岗",
###         "approvedCount": 35,
###         "effectiveDate": "2025-01-01"
###       }
###     ],
###     "approvalHistory": [
###       {
###         "approvalTime": "2024-12-15T14:30:00",
###         "approvalStatus": "已批准",
###         "approver": "校长",
###         "approvalComments": "同意该编制规划方案"
###       },
###       {
###         "approvalTime": "2024-12-12T10:15:00",
###         "approvalStatus": "待审批",
###         "approver": "人事处长",
###         "approvalComments": "初审通过，提交校长审批"
###       }
###     ]
###   }
### }

### 27. 上传人事文件 - 编制文件
POST {{baseUrl}}/api/organizations/hr-documents/upload
Authorization: {{token}}
Content-Type: application/json

{
  "documentName": "2025年度编制规划文件",
  "documentYear": 2025,
  "documentType": "STAFFING_PLAN",
  "documentUrl": "http://oss.example.com/documents/2025/staffing_plan_2025.pdf",
  "fileSize": 2048000,
  "fileExtension": "pdf",
  "effectiveDate": "2025-01-01",
  "approvalStatus": "草稿",
  "description": "2025年度全校编制规划文件，包含各学院编制分配方案",
  "keywords": "编制,规划,2025,学院",
  "isPublic": false
}

### 28. 上传人事文件 - 机构变更文件
POST {{baseUrl}}/api/organizations/hr-documents/upload
Authorization: {{token}}
Content-Type: application/json

{
  "documentName": "2025年机构调整通知",
  "documentYear": 2025,
  "documentType": "INSTITUTION_CHANGE",
  "documentUrl": "http://oss.example.com/documents/2025/institution_change_notice.pdf",
  "fileSize": 1024000,
  "fileExtension": "pdf",
  "effectiveDate": "2025-03-01",
  "approvalStatus": "已批准",
  "description": "关于调整部分机构设置的通知文件",
  "keywords": "机构调整,组织架构,2025",
  "isPublic": true
}

### 29. 更新人事文件信息
POST {{baseUrl}}/api/organizations/hr-documents/update
Authorization: {{token}}
Content-Type: application/json

{
  "documentId": "doc_001",
  "documentName": "2025年度编制规划文件（修订版）",
  "approvalStatus": "已批准",
  "description": "2025年度全校编制规划文件修订版，根据实际情况调整编制分配",
  "keywords": "编制,规划,2025,修订版"
}

### 30. 删除人事文件 - 逻辑删除
POST {{baseUrl}}/api/organizations/hr-documents/delete
Authorization: {{token}}
Content-Type: application/json

{
  "documentId": "doc_003",
  "deleteReason": "文件内容过期，不再适用",
  "physicalDelete": false
}

### ==================== 机构变更日志 API ====================

### 31. 查询机构变更日志 - 全部日志
GET {{baseUrl}}/api/organizations/change-logs?page=0&size=20&sort=changeTime,desc
Authorization: {{token}}
Content-Type: application/json

### 预期响应示例 - 查询机构变更日志（全部日志）
### {
###   "code": 200,
###   "message": "查询成功",
###   "data": {
###     "content": [
###       {
###         "logId": "log_001",
###         "institutionId": "inst_001",
###         "institutionName": "计算机学院",
###         "changeType": "修改",
###         "changeDetailsJson": "{\"fieldName\": \"机构名称\", \"oldValue\": \"信息技术学院\", \"newValue\": \"计算机学院\", \"operator\": \"张三\"}",
###         "changeDetails": {
###           "fieldName": "机构名称",
###           "oldValue": "信息技术学院",
###           "newValue": "计算机学院",
###           "operator": "张三"
###         },
###         "changeTime": "2025-01-15T14:30:00",
###         "operatorId": "user_001",
###         "operatorName": "张三",
###         "approvalDocNumber": "杭科院〔2025〕003号",
###         "hrDocumentId": "doc_002",
###         "hrDocumentName": "2025年机构调整通知",
###         "impactScope": "本机构及2个子机构",
###         "beforeStatus": "NORMAL",
###         "afterStatus": "NORMAL",
###         "affectedEmployeeCount": 45,
###         "remarks": "根据学科发展需要调整机构名称"
###       },
###       {
###         "logId": "log_002",
###         "institutionId": "inst_002",
###         "institutionName": "机械工程学院",
###         "changeType": "划转",
###         "changeDetailsJson": "{\"fieldName\": \"上级机构\", \"oldValue\": \"工程技术部\", \"newValue\": \"教学单位\", \"operator\": \"李四\"}",
###         "changeDetails": {
###           "fieldName": "上级机构",
###           "oldValue": "工程技术部",
###           "newValue": "教学单位",
###           "operator": "李四"
###         },
###         "changeTime": "2025-01-10T10:15:00",
###         "operatorId": "user_002",
###         "operatorName": "李四",
###         "approvalDocNumber": "杭科院〔2025〕002号",
###         "hrDocumentId": "doc_002",
###         "hrDocumentName": "2025年机构调整通知",
###         "impactScope": "本机构及3个子机构",
###         "beforeStatus": "NORMAL",
###         "afterStatus": "NORMAL",
###         "affectedEmployeeCount": 42,
###         "remarks": "根据管理体制调整进行机构划转"
###       }
###     ],
###     "pageable": {
###       "sort": {
###         "sorted": true,
###         "unsorted": false
###       },
###       "pageNumber": 0,
###       "pageSize": 20,
###       "offset": 0,
###       "paged": true,
###       "unpaged": false
###     },
###     "totalElements": 25,
###     "totalPages": 2,
###     "last": false,
###     "first": true,
###     "numberOfElements": 20,
###     "size": 20,
###     "number": 0
###   }
### }

### 32. 查询机构变更日志 - 指定机构
GET {{baseUrl}}/api/organizations/change-logs?institutionId=inst_001&changeType=修改&changeTimeStart=2025-01-01T00:00:00&changeTimeEnd=2025-12-31T23:59:59
Authorization: {{token}}
Content-Type: application/json

### 预期响应示例 - 查询机构变更日志（指定机构）
### {
###   "code": 200,
###   "message": "查询成功",
###   "data": {
###     "content": [
###       {
###         "logId": "log_001",
###         "institutionId": "inst_001",
###         "institutionName": "计算机学院",
###         "changeType": "修改",
###         "changeDetailsJson": "{\"fieldName\": \"机构名称\", \"oldValue\": \"信息技术学院\", \"newValue\": \"计算机学院\", \"operator\": \"张三\"}",
###         "changeTime": "2025-01-15T14:30:00",
###         "operatorId": "user_001",
###         "operatorName": "张三",
###         "approvalDocNumber": "杭科院〔2025〕003号",
###         "hrDocumentId": "doc_002",
###         "hrDocumentName": "2025年机构调整通知",
###         "impactScope": "本机构及2个子机构",
###         "beforeStatus": "NORMAL",
###         "afterStatus": "NORMAL",
###         "affectedEmployeeCount": 45,
###         "remarks": "根据学科发展需要调整机构名称"
###       }
###     ],
###     "pageable": {
###       "sort": {
###         "sorted": true,
###         "unsorted": false
###       },
###       "pageNumber": 0,
###       "pageSize": 20,
###       "offset": 0,
###       "paged": true,
###       "unpaged": false
###     },
###     "totalElements": 1,
###     "totalPages": 1,
###     "last": true,
###     "first": true,
###     "numberOfElements": 1,
###     "size": 20,
###     "number": 0
###   }
### }

### 33. 查询机构变更日志 - 包含详情
GET {{baseUrl}}/api/organizations/change-logs?operatorName=张三&includeChangeDetails=true
Authorization: {{token}}
Content-Type: application/json

### 预期响应示例 - 查询机构变更日志（包含详情）
### {
###   "code": 200,
###   "message": "查询成功",
###   "data": {
###     "content": [
###       {
###         "logId": "log_001",
###         "institutionId": "inst_001",
###         "institutionName": "计算机学院",
###         "changeType": "修改",
###         "changeDetailsJson": "{\"changes\": [{\"fieldName\": \"机构名称\", \"oldValue\": \"信息技术学院\", \"newValue\": \"计算机学院\"}, {\"fieldName\": \"职责描述\", \"oldValue\": \"负责信息技术相关工作\", \"newValue\": \"负责计算机相关专业的教学科研工作\"}], \"operator\": \"张三\", \"operatorId\": \"user_001\", \"changeReason\": \"根据学科发展需要调整\"}",
###         "changeDetails": {
###           "changes": [
###             {
###               "fieldName": "机构名称",
###               "oldValue": "信息技术学院",
###               "newValue": "计算机学院"
###             },
###             {
###               "fieldName": "职责描述",
###               "oldValue": "负责信息技术相关工作",
###               "newValue": "负责计算机相关专业的教学科研工作"
###             }
###           ],
###           "operator": "张三",
###           "operatorId": "user_001",
###           "changeReason": "根据学科发展需要调整"
###         },
###         "changeTime": "2025-01-15T14:30:00",
###         "operatorId": "user_001",
###         "operatorName": "张三",
###         "approvalDocNumber": "杭科院〔2025〕003号",
###         "hrDocumentId": "doc_002",
###         "hrDocumentName": "2025年机构调整通知",
###         "impactScope": "本机构及2个子机构",
###         "beforeStatus": "NORMAL",
###         "afterStatus": "NORMAL",
###         "affectedEmployeeCount": 45,
###         "remarks": "根据学科发展需要调整机构名称和职责描述"
###       }
###     ],
###     "pageable": {
###       "sort": {
###         "sorted": true,
###         "unsorted": false
###       },
###       "pageNumber": 0,
###       "pageSize": 20,
###       "offset": 0,
###       "paged": true,
###       "unpaged": false
###     },
###     "totalElements": 3,
###     "totalPages": 1,
###     "last": true,
###     "first": true,
###     "numberOfElements": 3,
###     "size": 20,
###     "number": 0
###   }
### }

### ==================== 岗位序列管理 API ====================

### 34. 查询岗位序列列表 - 全部
GET {{baseUrl}}/api/organizations/positionSequences
Authorization: {{token}}
Content-Type: application/json

### 预期响应示例 - 查询岗位序列列表（全部）
### {
###   "code": 200,
###   "message": "查询成功",
###   "data": [
###     {
###       "sequenceId": "seq_001",
###       "sequenceName": "管理岗",
###       "sequenceCode": "MGMT",
###       "description": "管理岗位序列，包含各级管理职务",
###       "status": "NORMAL",
###       "sortOrder": 1,
###       "levelCount": 10,
###       "createTime": "2025-01-01T09:00:00",
###       "updateTime": "2025-01-05T10:30:00",
###       "levels": [
###         {
###           "levelId": "level_001",
###           "sequenceId": "seq_001",
###           "levelName": "一级",
###           "levelCode": "MGMT_01",
###           "levelOrder": 1,
###           "description": "管理岗一级，对应处级干部",
###           "status": "NORMAL",
###           "createTime": "2025-01-01T09:30:00",
###           "updateTime": "2025-01-01T09:30:00"
###         },
###         {
###           "levelId": "level_002",
###           "sequenceId": "seq_001",
###           "levelName": "二级",
###           "levelCode": "MGMT_02",
###           "levelOrder": 2,
###           "description": "管理岗二级，对应科级干部",
###           "status": "NORMAL",
###           "createTime": "2025-01-01T09:35:00",
###           "updateTime": "2025-01-01T09:35:00"
###         }
###       ]
###     },
###     {
###       "sequenceId": "seq_002",
###       "sequenceName": "专技岗",
###       "sequenceCode": "TECH",
###       "description": "专业技术岗位序列，包含教师和其他专技人员职务",
###       "status": "NORMAL",
###       "sortOrder": 2,
###       "levelCount": 13,
###       "createTime": "2025-01-01T09:10:00",
###       "updateTime": "2025-01-05T10:45:00",
###       "levels": [
###         {
###           "levelId": "level_003",
###           "sequenceId": "seq_002",
###           "levelName": "正高级",
###           "levelCode": "TECH_01",
###           "levelOrder": 1,
###           "description": "专技岗正高级，对应教授、研究员等",
###           "status": "NORMAL",
###           "createTime": "2025-01-01T09:40:00",
###           "updateTime": "2025-01-01T09:40:00"
###         }
###       ]
###     },
###     {
###       "sequenceId": "seq_003",
###       "sequenceName": "工勤岗",
###       "sequenceCode": "WORKER",
###       "description": "工勤岗位序列，包含各类工勤人员职务",
###       "status": "DISABLED",
###       "sortOrder": 3,
###       "levelCount": 5,
###       "createTime": "2025-01-01T09:20:00",
###       "updateTime": "2025-01-10T14:20:00",
###       "levels": []
###     }
###   ]
### }

### 35. 查询岗位序列列表 - 正常状态
GET {{baseUrl}}/api/organizations/positionSequences?status=NORMAL
Authorization: {{token}}
Content-Type: application/json

### 预期响应示例 - 查询岗位序列列表（正常状态）
### {
###   "code": 200,
###   "message": "查询成功",
###   "data": [
###     {
###       "sequenceId": "seq_001",
###       "sequenceName": "管理岗",
###       "sequenceCode": "MGMT",
###       "description": "管理岗位序列，包含各级管理职务",
###       "status": "NORMAL",
###       "sortOrder": 1,
###       "levelCount": 10,
###       "createTime": "2025-01-01T09:00:00",
###       "updateTime": "2025-01-05T10:30:00"
###     },
###     {
###       "sequenceId": "seq_002",
###       "sequenceName": "专技岗",
###       "sequenceCode": "TECH",
###       "description": "专业技术岗位序列，包含教师和其他专技人员职务",
###       "status": "NORMAL",
###       "sortOrder": 2,
###       "levelCount": 13,
###       "createTime": "2025-01-01T09:10:00",
###       "updateTime": "2025-01-05T10:45:00"
###     }
###   ]
### }

### 36. 创建岗位序列 - 管理岗
POST {{baseUrl}}/api/organizations/positionSequences/create
Authorization: {{token}}
Content-Type: application/json

{
  "sequenceName": "管理岗",
  "sequenceCode": "MGMT",
  "description": "管理岗位序列，包含各级管理职务",
  "sortOrder": 1
}

### 37. 创建岗位序列 - 专技岗
POST {{baseUrl}}/api/organizations/positionSequences/create
Authorization: {{token}}
Content-Type: application/json

{
  "sequenceName": "专技岗",
  "sequenceCode": "TECH",
  "description": "专业技术岗位序列，包含教师和其他专技人员职务",
  "sortOrder": 2
}

### 38. 查询岗位等级列表
GET {{baseUrl}}/api/organizations/positionLevels?sequenceId=seq_001
Authorization: {{token}}
Content-Type: application/json

### 预期响应示例 - 查询岗位等级列表
### {
###   "code": 200,
###   "message": "查询成功",
###   "data": [
###     {
###       "levelId": "level_001",
###       "sequenceId": "seq_001",
###       "sequenceName": "管理岗",
###       "levelName": "一级",
###       "levelCode": "MGMT_01",
###       "levelOrder": 1,
###       "description": "管理岗一级，对应处级干部",
###       "status": "NORMAL",
###       "createTime": "2025-01-01T09:30:00",
###       "updateTime": "2025-01-01T09:30:00"
###     },
###     {
###       "levelId": "level_002",
###       "sequenceId": "seq_001",
###       "sequenceName": "管理岗",
###       "levelName": "二级",
###       "levelCode": "MGMT_02",
###       "levelOrder": 2,
###       "description": "管理岗二级，对应科级干部",
###       "status": "NORMAL",
###       "createTime": "2025-01-01T09:35:00",
###       "updateTime": "2025-01-01T09:35:00"
###     },
###     {
###       "levelId": "level_003",
###       "sequenceId": "seq_001",
###       "sequenceName": "管理岗",
###       "levelName": "三级",
###       "levelCode": "MGMT_03",
###       "levelOrder": 3,
###       "description": "管理岗三级，对应副科级干部",
###       "status": "NORMAL",
###       "createTime": "2025-01-01T09:40:00",
###       "updateTime": "2025-01-01T09:40:00"
###     },
###     {
###       "levelId": "level_004",
###       "sequenceId": "seq_001",
###       "sequenceName": "管理岗",
###       "levelName": "四级",
###       "levelCode": "MGMT_04",
###       "levelOrder": 4,
###       "description": "管理岗四级，对应主任科员",
###       "status": "NORMAL",
###       "createTime": "2025-01-01T09:45:00",
###       "updateTime": "2025-01-01T09:45:00"
###     },
###     {
###       "levelId": "level_005",
###       "sequenceId": "seq_001",
###       "sequenceName": "管理岗",
###       "levelName": "五级",
###       "levelCode": "MGMT_05",
###       "levelOrder": 5,
###       "description": "管理岗五级，对应副主任科员",
###       "status": "NORMAL",
###       "createTime": "2025-01-01T09:50:00",
###       "updateTime": "2025-01-01T09:50:00"
###     }
###   ]
### }

### 39. 创建岗位等级 - 管理岗一级
POST {{baseUrl}}/api/organizations/positionLevels/create
Authorization: {{token}}
Content-Type: application/json

{
  "sequenceId": "seq_001",
  "levelName": "一级",
  "levelCode": "MGMT_01",
  "levelOrder": 1,
  "description": "管理岗一级，对应处级干部"
}

### 40. 创建岗位等级 - 管理岗二级
POST {{baseUrl}}/api/organizations/positionLevels/create
Authorization: {{token}}
Content-Type: application/json

{
  "sequenceId": "seq_001",
  "levelName": "二级",
  "levelCode": "MGMT_02",
  "levelOrder": 2,
  "description": "管理岗二级，对应科级干部"
}

### ==================== 测试数据清理 ====================

### 清理测试数据 - 删除测试机构
POST {{baseUrl}}/api/organizations/institutions/updateStatus
Authorization: {{token}}
Content-Type: application/json

{
  "institutionId": "test_inst_001",
  "status": "WITHDRAWN",
  "reasonForChange": "测试完成，清理测试数据",
  "approvalDocNumber": "TEST_CLEANUP_001"
}

### 清理测试数据 - 删除测试编制规划
POST {{baseUrl}}/api/organizations/staffing-plans/delete
Authorization: {{token}}
Content-Type: application/json

{
  "planId": "test_plan_001",
  "deleteReason": "测试完成，清理测试数据"
}

### 清理测试数据 - 删除测试文件
POST {{baseUrl}}/api/organizations/hr-documents/delete
Authorization: {{token}}
Content-Type: application/json

{
  "documentId": "test_doc_001",
  "deleteReason": "测试完成，清理测试数据",
  "physicalDelete": true
}

### ==================== 错误场景测试 ====================

### 错误测试 - 创建重复机构编码
POST {{baseUrl}}/api/organizations/institutions/create
Authorization: {{token}}
Content-Type: application/json

{
  "institutionName": "测试重复编码学院",
  "institutionCode": "CS001",
  "institutionType": "COLLEGE",
  "isAdHoc": false,
  "reasonForChange": "测试重复编码错误处理"
}

### 错误测试 - 查询不存在的机构
GET {{baseUrl}}/api/organizations/institutions/detail?institutionId=non_exist_001
Authorization: {{token}}
Content-Type: application/json

### 预期响应示例 - 查询不存在的机构（404错误）
### {
###   "code": 404,
###   "message": "机构不存在",
###   "data": null,
###   "timestamp": "2025-08-07T15:30:00",
###   "path": "/api/organizations/institutions/detail",
###   "error": "NOT_FOUND",
###   "details": {
###     "institutionId": "non_exist_001",
###     "errorCode": "INSTITUTION_NOT_FOUND",
###     "suggestion": "请检查机构ID是否正确"
###   }
### }

### 错误测试 - 无效的机构类型
POST {{baseUrl}}/api/organizations/institutions/create
Authorization: {{token}}
Content-Type: application/json

{
  "institutionName": "测试无效类型",
  "institutionCode": "INVALID_001",
  "institutionType": "INVALID_TYPE",
  "isAdHoc": false,
  "reasonForChange": "测试无效类型错误处理"
}

### 错误测试 - 循环父子关系
POST {{baseUrl}}/api/organizations/institutions/transfer
Authorization: {{token}}
Content-Type: application/json

{
  "institutionId": "inst_parent_001",
  "newParentInstitutionId": "inst_child_001",
  "reason": "测试循环父子关系错误处理",
  "includeChildren": true,
  "updateEmployees": true
}

### 错误测试 - 编制规划重复
POST {{baseUrl}}/api/organizations/staffing-plans/create
Authorization: {{token}}
Content-Type: application/json

{
  "planYear": 2025,
  "institutionId": "inst_001",
  "positionCategory": "管理岗",
  "approvedCount": 30,
  "effectiveDate": "2025-01-01"
}

### ==================== 边界值测试 ====================

### 边界测试 - 最长机构名称
POST {{baseUrl}}/api/organizations/institutions/create
Authorization: {{token}}
Content-Type: application/json

{
  "institutionName": "这是一个非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常长的机构名称用于测试边界值",
  "institutionCode": "LONG_NAME_001",
  "institutionType": "DEPARTMENT",
  "isAdHoc": false,
  "reasonForChange": "测试最长机构名称边界值"
}

### 边界测试 - 最大编制数
POST {{baseUrl}}/api/organizations/staffing-plans/create
Authorization: {{token}}
Content-Type: application/json

{
  "planYear": 2025,
  "institutionId": "inst_002",
  "positionCategory": "专技岗",
  "approvedCount": 9999,
  "effectiveDate": "2025-01-01",
  "remarks": "测试最大编制数边界值"
}

### 边界测试 - 最小编制数
POST {{baseUrl}}/api/organizations/staffing-plans/create
Authorization: {{token}}
Content-Type: application/json

{
  "planYear": 2025,
  "institutionId": "inst_003",
  "positionCategory": "工勤岗",
  "approvedCount": 0,
  "effectiveDate": "2025-01-01",
  "remarks": "测试最小编制数边界值"
}

### 边界测试 - 最大文件大小
POST {{baseUrl}}/api/organizations/hr-documents/upload
Authorization: {{token}}
Content-Type: application/json

{
  "documentName": "大文件测试",
  "documentYear": 2025,
  "documentType": "OTHER",
  "documentUrl": "http://oss.example.com/documents/large_file.pdf",
  "fileSize": 104857600,
  "fileExtension": "pdf",
  "effectiveDate": "2025-01-01",
  "description": "测试最大文件大小边界值",
  "isPublic": false
}

### ==================== 性能测试 ====================

### 性能测试 - 大量数据查询
GET {{baseUrl}}/api/organizations/institutions?page=0&size=1000&includeChildren=true&includeStaffingStats=true
Authorization: {{token}}
Content-Type: application/json

### 预期响应示例 - 大量数据查询（性能测试）
### {
###   "code": 200,
###   "message": "查询成功",
###   "data": {
###     "content": [
###       "... 省略具体数据，实际包含1000条记录 ..."
###     ],
###     "pageable": {
###       "sort": {
###         "sorted": false,
###         "unsorted": true
###       },
###       "pageNumber": 0,
###       "pageSize": 1000,
###       "offset": 0,
###       "paged": true,
###       "unpaged": false
###     },
###     "totalElements": 1500,
###     "totalPages": 2,
###     "last": false,
###     "first": true,
###     "numberOfElements": 1000,
###     "size": 1000,
###     "number": 0,
###     "performanceMetrics": {
###       "queryTime": "245ms",
###       "totalRecords": 1500,
###       "returnedRecords": 1000,
###       "databaseQueryTime": "180ms",
###       "dataProcessingTime": "65ms"
###     }
###   }
### }

### 性能测试 - 复杂条件查询
GET {{baseUrl}}/api/organizations/change-logs?page=0&size=500&changeTimeStart=2020-01-01T00:00:00&changeTimeEnd=2025-12-31T23:59:59&includeChangeDetails=true&keyword=调整
Authorization: {{token}}
Content-Type: application/json

### 预期响应示例 - 复杂条件查询（性能测试）
### {
###   "code": 200,
###   "message": "查询成功",
###   "data": {
###     "content": [
###       "... 省略具体数据，实际包含500条记录 ..."
###     ],
###     "pageable": {
###       "sort": {
###         "sorted": true,
###         "unsorted": false
###       },
###       "pageNumber": 0,
###       "pageSize": 500,
###       "offset": 0,
###       "paged": true,
###       "unpaged": false
###     },
###     "totalElements": 2500,
###     "totalPages": 5,
###     "last": false,
###     "first": true,
###     "numberOfElements": 500,
###     "size": 500,
###     "number": 0,
###     "performanceMetrics": {
###       "queryTime": "680ms",
###       "totalRecords": 2500,
###       "returnedRecords": 500,
###       "databaseQueryTime": "520ms",
###       "dataProcessingTime": "160ms",
###       "searchConditions": {
###         "timeRangeFilter": "2020-01-01 to 2025-12-31",
###         "keywordSearch": "调整",
###         "includeDetails": true
###       }
###     }
###   }
### }

### ==================== 并发测试准备 ====================

### 并发测试 - 同时创建机构1
POST {{baseUrl}}/api/organizations/institutions/create
Authorization: {{token}}
Content-Type: application/json

{
  "institutionName": "并发测试机构1",
  "institutionCode": "CONCURRENT_001",
  "institutionType": "DEPARTMENT",
  "isAdHoc": false,
  "reasonForChange": "并发测试场景1"
}

### 并发测试 - 同时创建机构2
POST {{baseUrl}}/api/organizations/institutions/create
Authorization: {{token}}
Content-Type: application/json

{
  "institutionName": "并发测试机构2",
  "institutionCode": "CONCURRENT_002",
  "institutionType": "DEPARTMENT",
  "isAdHoc": false,
  "reasonForChange": "并发测试场景2"
}

### 并发测试 - 同时更新同一机构
POST {{baseUrl}}/api/organizations/institutions/update
Authorization: {{token}}
Content-Type: application/json

{
  "institutionId": "inst_concurrent_test",
  "institutionName": "并发更新测试机构A",
  "reasonForChange": "并发更新测试A"
}

### ==================== 权限测试 ====================

### 权限测试 - 无权限访问
GET {{baseUrl}}/api/organizations/institutions
Content-Type: application/json

### 预期响应示例 - 无权限访问（401错误）
### {
###   "code": 401,
###   "message": "未授权访问",
###   "data": null,
###   "timestamp": "2025-08-07T15:35:00",
###   "path": "/api/organizations/institutions",
###   "error": "UNAUTHORIZED",
###   "details": {
###     "errorCode": "MISSING_AUTHORIZATION",
###     "suggestion": "请在请求头中提供有效的Authorization Token"
###   }
### }

### 权限测试 - 无效Token
GET {{baseUrl}}/api/organizations/institutions
Authorization: Bearer invalid_token_here
Content-Type: application/json

### 预期响应示例 - 无效Token（401错误）
### {
###   "code": 401,
###   "message": "无效的访问令牌",
###   "data": null,
###   "timestamp": "2025-08-07T15:36:00",
###   "path": "/api/organizations/institutions",
###   "error": "UNAUTHORIZED",
###   "details": {
###     "errorCode": "INVALID_TOKEN",
###     "suggestion": "请提供有效的JWT Token"
###   }
### }

### 权限测试 - 过期Token
GET {{baseUrl}}/api/organizations/institutions
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.expired_token
Content-Type: application/json

### 预期响应示例 - 过期Token（401错误）
### {
###   "code": 401,
###   "message": "访问令牌已过期",
###   "data": null,
###   "timestamp": "2025-08-07T15:37:00",
###   "path": "/api/organizations/institutions",
###   "error": "UNAUTHORIZED",
###   "details": {
###     "errorCode": "TOKEN_EXPIRED",
###     "expiredAt": "2025-08-06T15:37:00",
###     "suggestion": "请重新登录获取新的访问令牌"
###   }
### }
