package com.hzwangda.hky.hr.modules.organization.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 机构查询条件DTO
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Data
@ApiModel("机构查询条件DTO")
public class InstitutionQueryCriteria {

    @ApiModelProperty(value = "机构名称")
    private String institutionName;

    @ApiModelProperty(value = "机构编码")
    private String institutionCode;

    @ApiModelProperty(value = "机构类型")
    private String institutionType;

    @ApiModelProperty(value = "是否为非常设机构")
    private Boolean isAdHoc;

    @ApiModelProperty(value = "上级机构ID")
    private String parentInstitutionId;

    @ApiModelProperty(value = "机构状态")
    private String status;

    @ApiModelProperty(value = "负责人ID")
    private String leaderId;

    @ApiModelProperty(value = "机构层级")
    private Integer level;

    @ApiModelProperty(value = "是否包含子机构")
    private Boolean includeChildren = false;

    @ApiModelProperty(value = "是否包含编制统计")
    private Boolean includeStaffingStats = false;
}
