package com.hzwangda.hky.hr.modules.organization.entity;

import com.wangda.oa.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 机构变更记录表实体类
 * 记录所有机构的变更历史，支持数据追溯
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Entity
@Data
@EqualsAndHashCode(callSuper = true)
@Table(name = "org_institution_change_log")
@ApiModel("机构变更记录")
public class InstitutionChangeLog extends BaseEntity {

    @Id
    @Column(name = "log_id", length = 36)
    @ApiModelProperty(value = "日志ID", hidden = true)
    @GeneratedValue(generator = "uuid")
    @GenericGenerator(name = "uuid", strategy = "uuid2")
    private String logId;

    @Column(name = "institution_id", nullable = false, length = 36)
    @NotBlank(message = "机构ID不能为空")
    @ApiModelProperty(value = "机构ID", required = true)
    private String institutionId;

    @Column(name = "change_type", length = 50)
    @ApiModelProperty(value = "变更类型")
    private String changeType;

    @Column(name = "change_details_json", columnDefinition = "TEXT")
    @ApiModelProperty(value = "变更详情JSON")
    private String changeDetailsJson;

    @Column(name = "change_time", nullable = false)
    @NotNull(message = "变更时间不能为空")
    @ApiModelProperty(value = "变更发生时间", required = true)
    private LocalDateTime changeTime;

    @Column(name = "operator_id", length = 36)
    @ApiModelProperty(value = "操作人ID")
    private String operatorId;

    @Column(name = "approval_doc_number", length = 100)
    @ApiModelProperty(value = "批文号")
    private String approvalDocNumber;

    @Column(name = "hr_document_id", length = 36)
    @ApiModelProperty(value = "人事文件ID")
    private String hrDocumentId;

    @Column(name = "impact_scope", length = 200)
    @ApiModelProperty(value = "影响范围")
    private String impactScope;

    @Column(name = "before_status", length = 20)
    @ApiModelProperty(value = "变更前状态")
    private String beforeStatus;

    @Column(name = "after_status", length = 20)
    @ApiModelProperty(value = "变更后状态")
    private String afterStatus;

    @Column(name = "affected_employee_count")
    @ApiModelProperty(value = "影响员工数量")
    private Integer affectedEmployeeCount;

    @Column(name = "remarks", columnDefinition = "TEXT")
    @ApiModelProperty(value = "备注")
    private String remarks;

    @Transient
    @ApiModelProperty(value = "机构名称")
    private String institutionName;

    @Transient
    @ApiModelProperty(value = "操作人姓名")
    private String operatorName;

    @Transient
    @ApiModelProperty(value = "人事文件名称")
    private String hrDocumentName;

    @Transient
    @ApiModelProperty(value = "变更详情对象")
    private Object changeDetails;
}
