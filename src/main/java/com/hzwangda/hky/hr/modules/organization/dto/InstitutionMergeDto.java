package com.hzwangda.hky.hr.modules.organization.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 机构合并DTO
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Data
@ApiModel("机构合并DTO")
public class InstitutionMergeDto {

    @NotEmpty(message = "源机构ID列表不能为空")
    @ApiModelProperty(value = "源机构ID列表", required = true)
    private List<String> sourceInstitutionIds;

    @ApiModelProperty(value = "目标机构ID")
    private String targetInstitutionId;

    @ApiModelProperty(value = "合并原因")
    private String mergeReason;

    @ApiModelProperty(value = "批文号")
    private String approvalDocNumber;

    @ApiModelProperty(value = "人事文件ID")
    private String hrDocumentId;

    @NotNull(message = "合并策略不能为空")
    @ApiModelProperty(value = "合并策略", required = true)
    private String mergeStrategy;

    @ApiModelProperty(value = "新机构信息")
    private InstitutionCreateDto newInstitutionInfo;

    @ApiModelProperty(value = "是否保留历史记录")
    private Boolean preserveHistory = true;

    @ApiModelProperty(value = "员工处理策略")
    private String employeeHandlingStrategy = "AUTO_TRANSFER";
}
