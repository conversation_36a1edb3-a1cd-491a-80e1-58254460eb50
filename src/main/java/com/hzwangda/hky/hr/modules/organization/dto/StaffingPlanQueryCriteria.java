package com.hzwangda.hky.hr.modules.organization.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 编制规划查询条件DTO
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Data
@ApiModel("编制规划查询条件DTO")
public class StaffingPlanQueryCriteria {

    @ApiModelProperty(value = "规划年度")
    private Integer planYear;

    @ApiModelProperty(value = "机构ID")
    private String institutionId;

    @ApiModelProperty(value = "机构名称")
    private String institutionName;

    @ApiModelProperty(value = "岗位类别")
    private String positionCategory;

    @ApiModelProperty(value = "审批状态")
    private String approvalStatus;

    @ApiModelProperty(value = "人事文件ID")
    private String hrDocumentId;

    @ApiModelProperty(value = "是否包含历史版本")
    private Boolean includeHistory = false;

    @ApiModelProperty(value = "是否包含统计信息")
    private Boolean includeStats = false;
}
