package com.hzwangda.hky.hr.modules.organization.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Min;
import java.time.LocalDate;

/**
 * 编制规划更新DTO
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Data
@ApiModel("编制规划更新DTO")
public class StaffingPlanUpdateDto {

    @NotBlank(message = "规划ID不能为空")
    @ApiModelProperty(value = "规划ID", required = true)
    private String planId;

    @Min(value = 0, message = "核定编制数不能小于0")
    @ApiModelProperty(value = "核定编制数")
    private Integer approvedCount;

    @ApiModelProperty(value = "生效日期")
    private LocalDate effectiveDate;

    @ApiModelProperty(value = "结束日期")
    private LocalDate endDate;

    @ApiModelProperty(value = "人事文件ID")
    private String hrDocumentId;

    @ApiModelProperty(value = "审批状态")
    private String approvalStatus;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "是否创建新版本")
    private Boolean createNewVersion = false;
}
