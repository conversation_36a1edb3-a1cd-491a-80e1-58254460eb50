package com.hzwangda.hky.hr.modules.organization.repository;

import com.hzwangda.hky.hr.modules.organization.entity.Institution;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 机构Repository接口
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Repository
public interface InstitutionRepository extends JpaRepository<Institution, String>, JpaSpecificationExecutor<Institution> {

    /**
     * 根据机构编码查找机构
     * @param institutionCode 机构编码
     * @return 机构实体
     */
    Optional<Institution> findByInstitutionCode(String institutionCode);

    /**
     * 根据机构名称查找机构
     * @param institutionName 机构名称
     * @return 机构实体
     */
    Optional<Institution> findByInstitutionName(String institutionName);

    /**
     * 根据上级机构ID查找子机构
     * @param parentInstitutionId 上级机构ID
     * @return 子机构列表
     */
    List<Institution> findByParentInstitutionIdOrderBySortOrder(String parentInstitutionId);

    /**
     * 根据状态查找机构
     * @param status 机构状态
     * @return 机构列表
     */
    List<Institution> findByStatus(String status);

    /**
     * 根据机构类型查找机构
     * @param institutionType 机构类型
     * @return 机构列表
     */
    List<Institution> findByInstitutionType(String institutionType);

    /**
     * 根据是否为非常设机构查找
     * @param isAdHoc 是否为非常设机构
     * @return 机构列表
     */
    List<Institution> findByIsAdHoc(Boolean isAdHoc);

    /**
     * 查找根机构（没有上级机构的机构）
     * @return 根机构列表
     */
    @Query("SELECT i FROM Institution i WHERE i.parentInstitutionId IS NULL ORDER BY i.sortOrder")
    List<Institution> findRootInstitutions();

    /**
     * 递归查找机构及其所有子机构
     * @param institutionId 机构ID
     * @return 机构ID列表
     */
    @Query(value = "WITH RECURSIVE institution_tree AS (" +
            "  SELECT institution_id FROM org_institution WHERE institution_id = :institutionId " +
            "  UNION ALL " +
            "  SELECT i.institution_id FROM org_institution i " +
            "  INNER JOIN institution_tree it ON i.parent_institution_id = it.institution_id" +
            ") SELECT institution_id FROM institution_tree", nativeQuery = true)
    List<String> findInstitutionTreeIds(@Param("institutionId") String institutionId);

    /**
     * 检查是否存在循环父子关系
     * @param institutionId 机构ID
     * @param parentInstitutionId 父机构ID
     * @return 是否存在循环关系
     */
    @Query(value = "WITH RECURSIVE parent_tree AS (" +
            "  SELECT institution_id, parent_institution_id FROM org_institution WHERE institution_id = :parentInstitutionId " +
            "  UNION ALL " +
            "  SELECT i.institution_id, i.parent_institution_id FROM org_institution i " +
            "  INNER JOIN parent_tree pt ON i.institution_id = pt.parent_institution_id" +
            ") SELECT COUNT(*) > 0 FROM parent_tree WHERE institution_id = :institutionId", nativeQuery = true)
    Boolean hasCircularReference(@Param("institutionId") String institutionId, @Param("parentInstitutionId") String parentInstitutionId);
}
