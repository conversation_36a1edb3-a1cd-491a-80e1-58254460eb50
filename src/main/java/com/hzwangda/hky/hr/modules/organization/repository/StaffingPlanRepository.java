package com.hzwangda.hky.hr.modules.organization.repository;

import com.hzwangda.hky.hr.modules.organization.entity.StaffingPlan;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * 编制规划Repository接口
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Repository
public interface StaffingPlanRepository extends JpaRepository<StaffingPlan, String>, JpaSpecificationExecutor<StaffingPlan> {

    /**
     * 根据机构ID和年度查找编制规划
     * @param institutionId 机构ID
     * @param planYear 规划年度
     * @return 编制规划列表
     */
    List<StaffingPlan> findByInstitutionIdAndPlanYear(String institutionId, Integer planYear);

    /**
     * 根据机构ID、年度和岗位类别查找编制规划
     * @param institutionId 机构ID
     * @param planYear 规划年度
     * @param positionCategory 岗位类别
     * @return 编制规划
     */
    Optional<StaffingPlan> findByInstitutionIdAndPlanYearAndPositionCategory(
            String institutionId, Integer planYear, String positionCategory);

    /**
     * 根据年度查找编制规划
     * @param planYear 规划年度
     * @return 编制规划列表
     */
    List<StaffingPlan> findByPlanYear(Integer planYear);

    /**
     * 根据机构ID查找编制规划
     * @param institutionId 机构ID
     * @return 编制规划列表
     */
    List<StaffingPlan> findByInstitutionId(String institutionId);

    /**
     * 根据岗位类别查找编制规划
     * @param positionCategory 岗位类别
     * @return 编制规划列表
     */
    List<StaffingPlan> findByPositionCategory(String positionCategory);

    /**
     * 根据审批状态查找编制规划
     * @param approvalStatus 审批状态
     * @return 编制规划列表
     */
    List<StaffingPlan> findByApprovalStatus(String approvalStatus);

    /**
     * 查找当前有效的编制规划
     * @param institutionId 机构ID
     * @param positionCategory 岗位类别
     * @param currentDate 当前日期
     * @return 编制规划
     */
    @Query("SELECT sp FROM StaffingPlan sp WHERE sp.institutionId = :institutionId " +
            "AND sp.positionCategory = :positionCategory " +
            "AND sp.effectiveDate <= :currentDate " +
            "AND (sp.endDate IS NULL OR sp.endDate >= :currentDate) " +
            "AND sp.approvalStatus = '已批准' " +
            "ORDER BY sp.effectiveDate DESC")
    Optional<StaffingPlan> findCurrentEffectivePlan(@Param("institutionId") String institutionId,
                                                     @Param("positionCategory") String positionCategory,
                                                     @Param("currentDate") LocalDate currentDate);

    /**
     * 根据人事文件ID查找编制规划
     * @param hrDocumentId 人事文件ID
     * @return 编制规划列表
     */
    List<StaffingPlan> findByHrDocumentId(String hrDocumentId);

    /**
     * 统计机构的编制总数
     * @param institutionId 机构ID
     * @param planYear 规划年度
     * @return 编制总数
     */
    @Query("SELECT COALESCE(SUM(sp.approvedCount), 0) FROM StaffingPlan sp " +
            "WHERE sp.institutionId = :institutionId AND sp.planYear = :planYear " +
            "AND sp.approvalStatus = '已批准'")
    Integer sumApprovedCountByInstitutionAndYear(@Param("institutionId") String institutionId,
                                                 @Param("planYear") Integer planYear);
}
