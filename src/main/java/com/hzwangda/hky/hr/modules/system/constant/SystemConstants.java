package com.hzwangda.hky.hr.modules.system.constant;

import com.wangda.oa.utils.SecurityUtils;
import org.springframework.security.core.GrantedAuthority;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/5/24
 * @description 常量信息
 */
public class SystemConstants {

    /**
     * 是否是超级管理员
     * @return Boolean
     */
    public static Boolean isSuperAdmin() {
        List<String> elPermissions = SecurityUtils.getCurrentUser().getAuthorities().stream().map(GrantedAuthority::getAuthority).collect(Collectors.toList());
        if(Arrays.stream(new String[]{"admin"}).anyMatch(elPermissions::contains)) {
            return true;
        }
        return false;
    }
}
