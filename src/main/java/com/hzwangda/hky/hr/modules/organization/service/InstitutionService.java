package com.hzwangda.hky.hr.modules.organization.service;

import com.hzwangda.hky.hr.modules.organization.dto.*;
import com.hzwangda.hky.hr.modules.organization.entity.Institution;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 机构管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
public interface InstitutionService {

    /**
     * 查询机构列表
     * @param criteria 查询条件
     * @param pageable 分页参数
     * @return 机构分页列表
     */
    Page<InstitutionDto> queryAll(InstitutionQueryCriteria criteria, Pageable pageable);

    /**
     * 查询机构详情
     * @param institutionId 机构ID
     * @return 机构详情
     */
    InstitutionDto findById(String institutionId);

    /**
     * 创建机构
     * @param createDto 创建DTO
     * @return 机构DTO
     */
    InstitutionDto create(InstitutionCreateDto createDto);

    /**
     * 更新机构信息
     * @param updateDto 更新DTO
     * @return 机构DTO
     */
    InstitutionDto update(InstitutionUpdateDto updateDto);

    /**
     * 更新机构属性
     * @param propertiesDto 属性DTO
     * @return 机构DTO
     */
    InstitutionDto updateProperties(InstitutionPropertiesDto propertiesDto);

    /**
     * 更新机构状态
     * @param statusDto 状态DTO
     * @return 机构DTO
     */
    InstitutionDto updateStatus(InstitutionStatusDto statusDto);

    /**
     * 机构划转
     * @param transferDto 划转DTO
     * @return 操作结果
     */
    Boolean transfer(InstitutionTransferDto transferDto);

    /**
     * 机构合并
     * @param mergeDto 合并DTO
     * @return 操作结果
     */
    Boolean merge(InstitutionMergeDto mergeDto);

    /**
     * 查询机构树
     * @param rootInstitutionId 根机构ID
     * @return 机构树
     */
    List<InstitutionDto> getInstitutionTree(String rootInstitutionId);

    /**
     * 验证机构编码是否唯一
     * @param institutionCode 机构编码
     * @param excludeId 排除的机构ID
     * @return 是否唯一
     */
    Boolean validateInstitutionCode(String institutionCode, String excludeId);

    /**
     * 验证机构名称是否唯一
     * @param institutionName 机构名称
     * @param excludeId 排除的机构ID
     * @return 是否唯一
     */
    Boolean validateInstitutionName(String institutionName, String excludeId);

    /**
     * 检查是否存在循环父子关系
     * @param institutionId 机构ID
     * @param parentInstitutionId 父机构ID
     * @return 是否存在循环关系
     */
    Boolean checkCircularReference(String institutionId, String parentInstitutionId);

    /**
     * 获取机构的所有子机构ID
     * @param institutionId 机构ID
     * @return 子机构ID列表
     */
    List<String> getChildInstitutionIds(String institutionId);

    /**
     * 删除机构（逻辑删除）
     * @param institutionId 机构ID
     * @param deleteReason 删除原因
     * @return 操作结果
     */
    Boolean delete(String institutionId, String deleteReason);
}
