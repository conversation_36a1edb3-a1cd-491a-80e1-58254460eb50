package com.hzwangda.hky.hr.modules.organization.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 机构创建DTO
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Data
@ApiModel("机构创建DTO")
public class InstitutionCreateDto {

    @NotBlank(message = "机构名称不能为空")
    @Size(max = 200, message = "机构名称长度不能超过200个字符")
    @ApiModelProperty(value = "机构名称", required = true)
    private String institutionName;

    @NotBlank(message = "机构编码不能为空")
    @Size(max = 50, message = "机构编码长度不能超过50个字符")
    @ApiModelProperty(value = "机构编码", required = true)
    private String institutionCode;

    @ApiModelProperty(value = "机构类型")
    private String institutionType;

    @ApiModelProperty(value = "是否为非常设机构")
    private Boolean isAdHoc = false;

    @ApiModelProperty(value = "上级机构ID")
    private String parentInstitutionId;

    @ApiModelProperty(value = "职责描述")
    private String responsibilities;

    @ApiModelProperty(value = "相关附件URL")
    private String attachmentUrls;

    @ApiModelProperty(value = "负责人ID")
    private String leaderId;

    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    @ApiModelProperty(value = "办公地址")
    private String officeAddress;

    @ApiModelProperty(value = "排序号")
    private Integer sortOrder;

    @ApiModelProperty(value = "变更原因")
    private String reasonForChange;

    @ApiModelProperty(value = "批文号")
    private String approvalDocNumber;

    @ApiModelProperty(value = "人事文件ID")
    private String hrDocumentId;
}
