package com.hzwangda.hky.hr.modules.organization.dto;

import com.wangda.oa.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.List;

/**
 * 人事文件DTO
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("人事文件DTO")
public class HrDocumentDto extends BaseDTO {

    @ApiModelProperty(value = "文件ID")
    private String documentId;

    @ApiModelProperty(value = "文件名称")
    private String documentName;

    @ApiModelProperty(value = "文件所属年度")
    private Integer documentYear;

    @ApiModelProperty(value = "文件类型")
    private String documentType;

    @ApiModelProperty(value = "文件类型名称")
    private String documentTypeName;

    @ApiModelProperty(value = "文件存储URL")
    private String documentUrl;

    @ApiModelProperty(value = "文件大小")
    private Long fileSize;

    @ApiModelProperty(value = "文件扩展名")
    private String fileExtension;

    @ApiModelProperty(value = "文件生效日期")
    private LocalDate effectiveDate;

    @ApiModelProperty(value = "审批状态")
    private String approvalStatus;

    @ApiModelProperty(value = "文件描述")
    private String description;

    @ApiModelProperty(value = "关键词")
    private String keywords;

    @ApiModelProperty(value = "是否公开")
    private Boolean isPublic;

    @ApiModelProperty(value = "下载次数")
    private Integer downloadCount;

    @ApiModelProperty(value = "上传人姓名")
    private String uploadByName;

    @ApiModelProperty(value = "更新人姓名")
    private String updateByName;

    @ApiModelProperty(value = "关联的编制规划数量")
    private Integer relatedStaffingPlanCount;

    @ApiModelProperty(value = "关联的机构变更数量")
    private Integer relatedInstitutionChangeCount;

    @ApiModelProperty(value = "关联的编制规划列表")
    private List<StaffingPlanDto> relatedStaffingPlans;

    @ApiModelProperty(value = "审批历史")
    private List<ApprovalHistoryDto> approvalHistory;
}
