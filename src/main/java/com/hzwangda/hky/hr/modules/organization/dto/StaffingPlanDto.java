package com.hzwangda.hky.hr.modules.organization.dto;

import com.wangda.oa.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.List;

/**
 * 编制规划DTO
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("编制规划DTO")
public class StaffingPlanDto extends BaseDTO {

    @ApiModelProperty(value = "规划ID")
    private String planId;

    @ApiModelProperty(value = "规划年度")
    private Integer planYear;

    @ApiModelProperty(value = "机构ID")
    private String institutionId;

    @ApiModelProperty(value = "机构名称")
    private String institutionName;

    @ApiModelProperty(value = "岗位类别")
    private String positionCategory;

    @ApiModelProperty(value = "核定编制数")
    private Integer approvedCount;

    @ApiModelProperty(value = "生效日期")
    private LocalDate effectiveDate;

    @ApiModelProperty(value = "结束日期")
    private LocalDate endDate;

    @ApiModelProperty(value = "人事文件ID")
    private String hrDocumentId;

    @ApiModelProperty(value = "人事文件名称")
    private String hrDocumentName;

    @ApiModelProperty(value = "审批状态")
    private String approvalStatus;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "当前在编人数")
    private Integer currentCount;

    @ApiModelProperty(value = "超编人数")
    private Integer overCount;

    @ApiModelProperty(value = "缺编人数")
    private Integer shortageCount;

    @ApiModelProperty(value = "编制使用率")
    private Double utilizationRate;

    @ApiModelProperty(value = "历史版本列表")
    private List<StaffingPlanDto> historyVersions;

    @ApiModelProperty(value = "关联文档列表")
    private List<HrDocumentDto> relatedDocuments;
}
