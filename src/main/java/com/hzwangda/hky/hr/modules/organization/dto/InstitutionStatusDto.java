package com.hzwangda.hky.hr.modules.organization.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 机构状态更新DTO
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Data
@ApiModel("机构状态更新DTO")
public class InstitutionStatusDto {

    @NotBlank(message = "机构ID不能为空")
    @ApiModelProperty(value = "机构ID", required = true)
    private String institutionId;

    @NotBlank(message = "状态不能为空")
    @ApiModelProperty(value = "机构状态", required = true)
    private String status;

    @ApiModelProperty(value = "变更原因")
    private String reasonForChange;

    @ApiModelProperty(value = "批文号")
    private String approvalDocNumber;

    @ApiModelProperty(value = "人事文件ID")
    private String hrDocumentId;
}
