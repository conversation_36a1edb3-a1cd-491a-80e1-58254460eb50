package com.hzwangda.hky.hr.modules.organization.controller;

import com.hzwangda.hky.hr.modules.organization.dto.*;
import com.hzwangda.hky.hr.modules.organization.service.InstitutionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 机构管理Controller
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@Api(tags = "组织管理-机构管理")
@RequestMapping("/api/organizations/institutions")
public class InstitutionController {

    private final InstitutionService institutionService;

    @ApiOperation("查询机构列表")
    @GetMapping
    public ResponseEntity<Object> queryInstitutions(InstitutionQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(institutionService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @ApiOperation("查询机构详情")
    @GetMapping("/detail")
    public ResponseEntity<Object> getInstitutionDetail(@RequestParam String institutionId) {
        return new ResponseEntity<>(institutionService.findById(institutionId), HttpStatus.OK);
    }

    @ApiOperation("创建机构")
    @PostMapping("/create")
    public ResponseEntity<Object> createInstitution(@Valid @RequestBody InstitutionCreateDto createDto) {
        return new ResponseEntity<>(institutionService.create(createDto), HttpStatus.CREATED);
    }

    @ApiOperation("更新机构信息")
    @PostMapping("/update")
    public ResponseEntity<Object> updateInstitution(@Valid @RequestBody InstitutionUpdateDto updateDto) {
        return new ResponseEntity<>(institutionService.update(updateDto), HttpStatus.OK);
    }

    @ApiOperation("更新机构属性")
    @PostMapping("/updateProperties")
    public ResponseEntity<Object> updateInstitutionProperties(@Valid @RequestBody InstitutionPropertiesDto propertiesDto) {
        return new ResponseEntity<>(institutionService.updateProperties(propertiesDto), HttpStatus.OK);
    }

    @ApiOperation("更新机构状态")
    @PostMapping("/updateStatus")
    public ResponseEntity<Object> updateInstitutionStatus(@Valid @RequestBody InstitutionStatusDto statusDto) {
        return new ResponseEntity<>(institutionService.updateStatus(statusDto), HttpStatus.OK);
    }

    @ApiOperation("机构划转")
    @PostMapping("/transfer")
    public ResponseEntity<Object> transferInstitution(@Valid @RequestBody InstitutionTransferDto transferDto) {
        Boolean result = institutionService.transfer(transferDto);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @ApiOperation("机构合并")
    @PostMapping("/merge")
    public ResponseEntity<Object> mergeInstitutions(@Valid @RequestBody InstitutionMergeDto mergeDto) {
        Boolean result = institutionService.merge(mergeDto);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @ApiOperation("查询机构树")
    @GetMapping("/tree")
    public ResponseEntity<Object> getInstitutionTree(@RequestParam(required = false) String rootInstitutionId) {
        return new ResponseEntity<>(institutionService.getInstitutionTree(rootInstitutionId), HttpStatus.OK);
    }

    @ApiOperation("验证机构编码")
    @GetMapping("/validateCode")
    public ResponseEntity<Object> validateInstitutionCode(@RequestParam String institutionCode,
                                                          @RequestParam(required = false) String excludeId) {
        Boolean isValid = institutionService.validateInstitutionCode(institutionCode, excludeId);
        return new ResponseEntity<>(isValid, HttpStatus.OK);
    }

    @ApiOperation("验证机构名称")
    @GetMapping("/validateName")
    public ResponseEntity<Object> validateInstitutionName(@RequestParam String institutionName,
                                                          @RequestParam(required = false) String excludeId) {
        Boolean isValid = institutionService.validateInstitutionName(institutionName, excludeId);
        return new ResponseEntity<>(isValid, HttpStatus.OK);
    }

    @ApiOperation("删除机构")
    @PostMapping("/delete")
    public ResponseEntity<Object> deleteInstitution(@RequestParam String institutionId,
                                                    @RequestParam String deleteReason) {
        Boolean result = institutionService.delete(institutionId, deleteReason);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }
}
