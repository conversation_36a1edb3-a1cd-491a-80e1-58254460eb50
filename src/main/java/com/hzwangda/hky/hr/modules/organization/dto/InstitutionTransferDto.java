package com.hzwangda.hky.hr.modules.organization.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 机构划转DTO
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Data
@ApiModel("机构划转DTO")
public class InstitutionTransferDto {

    @NotBlank(message = "机构ID不能为空")
    @ApiModelProperty(value = "机构ID", required = true)
    private String institutionId;

    @NotBlank(message = "新上级机构ID不能为空")
    @ApiModelProperty(value = "新上级机构ID", required = true)
    private String newParentInstitutionId;

    @ApiModelProperty(value = "划转原因")
    private String reason;

    @ApiModelProperty(value = "批文号")
    private String approvalDocNumber;

    @ApiModelProperty(value = "人事文件ID")
    private String hrDocumentId;

    @ApiModelProperty(value = "是否包含子机构")
    private Boolean includeChildren = true;

    @ApiModelProperty(value = "是否更新员工信息")
    private Boolean updateEmployees = true;
}
