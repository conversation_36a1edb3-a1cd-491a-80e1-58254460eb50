package com.hzwangda.hky.hr.modules.system.enums;

import com.alibaba.fastjson.annotation.JSONType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * @Author: leec
 * @Date: 2022/05/05
 * @Description: 系统角色权限
 */
@Getter
@AllArgsConstructor
@ToString
@JSONType(serializeEnumAsJavaBean = true)
public enum SystemAuthorityEnum {

    AUTH_EMPLOYEE("AUTH_EMPLOYEE", "员工");

    private String value;
    private String name;

    public static SystemAuthorityEnum getByValue(String value) {
        for(SystemAuthorityEnum authorityEnum : SystemAuthorityEnum.values()) {
            if(authorityEnum.value.equals(value)) {
                return authorityEnum;
            }
        }
        return null;
    }
}
