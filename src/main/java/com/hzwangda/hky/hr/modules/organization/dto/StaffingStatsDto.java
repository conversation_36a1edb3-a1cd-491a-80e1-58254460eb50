package com.hzwangda.hky.hr.modules.organization.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 编制统计信息DTO
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Data
@ApiModel("编制统计信息DTO")
public class StaffingStatsDto {

    @ApiModelProperty(value = "核定编制数")
    private Integer approvedCount;

    @ApiModelProperty(value = "当前在编人数")
    private Integer currentCount;

    @ApiModelProperty(value = "超编人数")
    private Integer overCount;

    @ApiModelProperty(value = "缺编人数")
    private Integer shortageCount;

    @ApiModelProperty(value = "编制使用率")
    private Double utilizationRate;
}
