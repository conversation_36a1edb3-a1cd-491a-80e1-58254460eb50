package com.hzwangda.hky.hr.modules.organization.mapper;

import com.hzwangda.hky.hr.modules.organization.dto.InstitutionCreateDto;
import com.hzwangda.hky.hr.modules.organization.dto.InstitutionDto;
import com.hzwangda.hky.hr.modules.organization.entity.Institution;
import com.wangda.oa.base.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * 机构Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface InstitutionMapper extends BaseMapper<InstitutionDto, Institution> {

    /**
     * 转换为DTO
     * @param entity 实体
     * @return DTO
     */
    @Override
    InstitutionDto toDto(Institution entity);

    /**
     * 转换为实体
     * @param dto DTO
     * @return 实体
     */
    @Override
    Institution toEntity(InstitutionDto dto);

    /**
     * 批量转换为DTO
     * @param entityList 实体列表
     * @return DTO列表
     */
    @Override
    List<InstitutionDto> toDto(List<Institution> entityList);

    /**
     * 批量转换为实体
     * @param dtoList DTO列表
     * @return 实体列表
     */
    @Override
    List<Institution> toEntity(List<InstitutionDto> dtoList);

    /**
     * 创建时的映射，忽略ID和审计字段
     * @param createDto 创建DTO
     * @return 实体
     */
    @Mapping(target = "institutionId", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "createBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "parentInstitutionName", ignore = true)
    @Mapping(target = "leaderName", ignore = true)
    Institution createDtoToEntity(InstitutionCreateDto createDto);
}
