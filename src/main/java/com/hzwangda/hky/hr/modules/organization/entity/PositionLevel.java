package com.hzwangda.hky.hr.modules.organization.entity;

import com.wangda.oa.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 岗位等级表实体类
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Entity
@Data
@EqualsAndHashCode(callSuper = true)
@Table(name = "org_position_level")
@ApiModel("岗位等级")
public class PositionLevel extends BaseEntity {

    @Id
    @Column(name = "level_id", length = 36)
    @ApiModelProperty(value = "等级ID", hidden = true)
    @GeneratedValue(generator = "uuid")
    @GenericGenerator(name = "uuid", strategy = "uuid2")
    private String levelId;

    @Column(name = "sequence_id", nullable = false, length = 36)
    @NotBlank(message = "序列ID不能为空")
    @ApiModelProperty(value = "序列ID", required = true)
    private String sequenceId;

    @Column(name = "level_name", nullable = false, length = 100)
    @NotBlank(message = "等级名称不能为空")
    @Size(max = 100, message = "等级名称长度不能超过100个字符")
    @ApiModelProperty(value = "等级名称", required = true)
    private String levelName;

    @Column(name = "level_code", nullable = false, length = 50, unique = true)
    @NotBlank(message = "等级编码不能为空")
    @Size(max = 50, message = "等级编码长度不能超过50个字符")
    @ApiModelProperty(value = "等级编码", required = true)
    private String levelCode;

    @Column(name = "level_order")
    @ApiModelProperty(value = "等级排序")
    private Integer levelOrder;

    @Column(name = "description", columnDefinition = "TEXT")
    @ApiModelProperty(value = "等级描述")
    private String description;

    @Column(name = "status", length = 20)
    @ApiModelProperty(value = "状态")
    private String status = "NORMAL";

    @Transient
    @ApiModelProperty(value = "序列名称")
    private String sequenceName;
}
