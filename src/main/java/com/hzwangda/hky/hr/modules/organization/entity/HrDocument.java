package com.hzwangda.hky.hr.modules.organization.entity;

import com.wangda.oa.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDate;

/**
 * 人事文件表实体类
 * 用于集中管理各类人事相关文件
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Entity
@Data
@EqualsAndHashCode(callSuper = true)
@Table(name = "hr_document")
@ApiModel("人事文件")
public class HrDocument extends BaseEntity {

    @Id
    @Column(name = "document_id", length = 36)
    @ApiModelProperty(value = "文件ID", hidden = true)
    @GeneratedValue(generator = "uuid")
    @GenericGenerator(name = "uuid", strategy = "uuid2")
    private String documentId;

    @Column(name = "document_name", nullable = false, length = 200)
    @NotBlank(message = "文件名称不能为空")
    @Size(max = 200, message = "文件名称长度不能超过200个字符")
    @ApiModelProperty(value = "文件名称", required = true)
    private String documentName;

    @Column(name = "document_year")
    @ApiModelProperty(value = "文件所属年度")
    private Integer documentYear;

    @Column(name = "document_type", length = 50)
    @ApiModelProperty(value = "文件类型")
    private String documentType;

    @Column(name = "document_url", columnDefinition = "TEXT")
    @ApiModelProperty(value = "文件存储URL")
    private String documentUrl;

    @Column(name = "file_size")
    @ApiModelProperty(value = "文件大小")
    private Long fileSize;

    @Column(name = "file_extension", length = 10)
    @ApiModelProperty(value = "文件扩展名")
    private String fileExtension;

    @Column(name = "effective_date")
    @ApiModelProperty(value = "文件生效日期")
    private LocalDate effectiveDate;

    @Column(name = "approval_status", length = 20)
    @ApiModelProperty(value = "审批状态")
    private String approvalStatus = "草稿";

    @Column(name = "description", columnDefinition = "TEXT")
    @ApiModelProperty(value = "文件描述")
    private String description;

    @Column(name = "keywords", length = 500)
    @ApiModelProperty(value = "关键词")
    private String keywords;

    @Column(name = "is_public")
    @ApiModelProperty(value = "是否公开")
    private Boolean isPublic = false;

    @Column(name = "download_count")
    @ApiModelProperty(value = "下载次数")
    private Integer downloadCount = 0;

    @Transient
    @ApiModelProperty(value = "文件类型名称")
    private String documentTypeName;

    @Transient
    @ApiModelProperty(value = "上传人姓名")
    private String uploadByName;

    @Transient
    @ApiModelProperty(value = "更新人姓名")
    private String updateByName;

    @Transient
    @ApiModelProperty(value = "关联的编制规划数量")
    private Integer relatedStaffingPlanCount;

    @Transient
    @ApiModelProperty(value = "关联的机构变更数量")
    private Integer relatedInstitutionChangeCount;
}
