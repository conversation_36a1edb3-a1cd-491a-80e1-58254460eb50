package com.hzwangda.hky.hr.modules.organization.entity;

import com.wangda.oa.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * 编制规划表实体类
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Entity
@Data
@EqualsAndHashCode(callSuper = true)
@Table(name = "org_staffing_plan")
@ApiModel("编制规划")
public class StaffingPlan extends BaseEntity {

    @Id
    @Column(name = "plan_id", length = 36)
    @ApiModelProperty(value = "规划ID", hidden = true)
    @GeneratedValue(generator = "uuid")
    @GenericGenerator(name = "uuid", strategy = "uuid2")
    private String planId;

    @Column(name = "plan_year", nullable = false)
    @NotNull(message = "规划年度不能为空")
    @ApiModelProperty(value = "规划年度", required = true)
    private Integer planYear;

    @Column(name = "institution_id", nullable = false, length = 36)
    @NotNull(message = "机构ID不能为空")
    @ApiModelProperty(value = "机构ID", required = true)
    private String institutionId;

    @Column(name = "position_category", length = 50)
    @ApiModelProperty(value = "岗位类别")
    private String positionCategory;

    @Column(name = "approved_count")
    @ApiModelProperty(value = "核定编制数")
    private Integer approvedCount;

    @Column(name = "effective_date")
    @ApiModelProperty(value = "生效日期")
    private LocalDate effectiveDate;

    @Column(name = "end_date")
    @ApiModelProperty(value = "结束日期")
    private LocalDate endDate;

    @Column(name = "hr_document_id", length = 36)
    @ApiModelProperty(value = "人事文件ID")
    private String hrDocumentId;

    @Column(name = "approval_status", length = 20)
    @ApiModelProperty(value = "审批状态")
    private String approvalStatus = "草稿";

    @Column(name = "remarks", columnDefinition = "TEXT")
    @ApiModelProperty(value = "备注")
    private String remarks;

    @Transient
    @ApiModelProperty(value = "机构名称")
    private String institutionName;

    @Transient
    @ApiModelProperty(value = "人事文件名称")
    private String hrDocumentName;

    @Transient
    @ApiModelProperty(value = "当前在编人数")
    private Integer currentCount;

    @Transient
    @ApiModelProperty(value = "超编人数")
    private Integer overCount;

    @Transient
    @ApiModelProperty(value = "缺编人数")
    private Integer shortageCount;

    @Transient
    @ApiModelProperty(value = "编制使用率")
    private Double utilizationRate;
}
