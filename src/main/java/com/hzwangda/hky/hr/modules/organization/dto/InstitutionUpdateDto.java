package com.hzwangda.hky.hr.modules.organization.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 机构更新DTO
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Data
@ApiModel("机构更新DTO")
public class InstitutionUpdateDto {

    @NotBlank(message = "机构ID不能为空")
    @ApiModelProperty(value = "机构ID", required = true)
    private String institutionId;

    @Size(max = 200, message = "机构名称长度不能超过200个字符")
    @ApiModelProperty(value = "机构名称")
    private String institutionName;

    @Size(max = 50, message = "机构编码长度不能超过50个字符")
    @ApiModelProperty(value = "机构编码")
    private String institutionCode;

    @ApiModelProperty(value = "机构类型")
    private String institutionType;

    @ApiModelProperty(value = "职责描述")
    private String responsibilities;

    @ApiModelProperty(value = "相关附件URL")
    private String attachmentUrls;

    @ApiModelProperty(value = "变更原因")
    private String reasonForChange;

    @ApiModelProperty(value = "批文号")
    private String approvalDocNumber;

    @ApiModelProperty(value = "人事文件ID")
    private String hrDocumentId;
}
