package com.hzwangda.hky.hr.modules.organization.repository;

import com.hzwangda.hky.hr.modules.organization.entity.InstitutionChangeLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 机构变更记录Repository接口
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Repository
public interface InstitutionChangeLogRepository extends JpaRepository<InstitutionChangeLog, String>, JpaSpecificationExecutor<InstitutionChangeLog> {

    /**
     * 根据机构ID查找变更记录
     * @param institutionId 机构ID
     * @return 变更记录列表
     */
    List<InstitutionChangeLog> findByInstitutionIdOrderByChangeTimeDesc(String institutionId);

    /**
     * 根据变更类型查找记录
     * @param changeType 变更类型
     * @return 变更记录列表
     */
    List<InstitutionChangeLog> findByChangeType(String changeType);

    /**
     * 根据操作人查找记录
     * @param operatorId 操作人ID
     * @return 变更记录列表
     */
    List<InstitutionChangeLog> findByOperatorId(String operatorId);

    /**
     * 根据时间范围查找记录
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 变更记录列表
     */
    List<InstitutionChangeLog> findByChangeTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据人事文件ID查找记录
     * @param hrDocumentId 人事文件ID
     * @return 变更记录列表
     */
    List<InstitutionChangeLog> findByHrDocumentId(String hrDocumentId);

    /**
     * 根据批文号查找记录
     * @param approvalDocNumber 批文号
     * @return 变更记录列表
     */
    List<InstitutionChangeLog> findByApprovalDocNumber(String approvalDocNumber);
}
