package com.hzwangda.hky.hr.modules.organization.service;

import com.hzwangda.hky.hr.modules.organization.dto.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * 编制规划Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
public interface StaffingPlanService {

    /**
     * 查询编制规划列表
     * @param criteria 查询条件
     * @param pageable 分页参数
     * @return 编制规划分页列表
     */
    Page<StaffingPlanDto> queryAll(StaffingPlanQueryCriteria criteria, Pageable pageable);

    /**
     * 查询编制规划详情
     * @param planId 规划ID
     * @return 编制规划详情
     */
    StaffingPlanDto findById(String planId);

    /**
     * 创建编制规划
     * @param createDto 创建DTO
     * @return 编制规划DTO
     */
    StaffingPlanDto create(StaffingPlanCreateDto createDto);

    /**
     * 更新编制规划
     * @param updateDto 更新DTO
     * @return 编制规划DTO
     */
    StaffingPlanDto update(StaffingPlanUpdateDto updateDto);

    /**
     * 删除编制规划
     * @param planId 规划ID
     * @param deleteReason 删除原因
     * @return 操作结果
     */
    Boolean delete(String planId, String deleteReason);

    /**
     * 获取当前有效的编制规划
     * @param institutionId 机构ID
     * @param positionCategory 岗位类别
     * @return 编制规划DTO
     */
    StaffingPlanDto getCurrentEffectivePlan(String institutionId, String positionCategory);

    /**
     * 验证编制规划是否重复
     * @param institutionId 机构ID
     * @param planYear 规划年度
     * @param positionCategory 岗位类别
     * @param excludeId 排除的规划ID
     * @return 是否重复
     */
    Boolean validateStaffingPlan(String institutionId, Integer planYear, String positionCategory, String excludeId);
}
