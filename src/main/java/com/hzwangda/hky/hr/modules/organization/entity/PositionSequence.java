package com.hzwangda.hky.hr.modules.organization.entity;

import com.wangda.oa.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 岗位序列表实体类
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Entity
@Data
@EqualsAndHashCode(callSuper = true)
@Table(name = "org_position_sequence")
@ApiModel("岗位序列")
public class PositionSequence extends BaseEntity {

    @Id
    @Column(name = "sequence_id", length = 36)
    @ApiModelProperty(value = "序列ID", hidden = true)
    @GeneratedValue(generator = "uuid")
    @GenericGenerator(name = "uuid", strategy = "uuid2")
    private String sequenceId;

    @Column(name = "sequence_name", nullable = false, length = 100)
    @NotBlank(message = "序列名称不能为空")
    @Size(max = 100, message = "序列名称长度不能超过100个字符")
    @ApiModelProperty(value = "序列名称", required = true)
    private String sequenceName;

    @Column(name = "sequence_code", nullable = false, length = 50, unique = true)
    @NotBlank(message = "序列编码不能为空")
    @Size(max = 50, message = "序列编码长度不能超过50个字符")
    @ApiModelProperty(value = "序列编码", required = true)
    private String sequenceCode;

    @Column(name = "description", columnDefinition = "TEXT")
    @ApiModelProperty(value = "序列描述")
    private String description;

    @Column(name = "status", length = 20)
    @ApiModelProperty(value = "状态")
    private String status = "NORMAL";

    @Column(name = "sort_order")
    @ApiModelProperty(value = "排序号")
    private Integer sortOrder;

    @Transient
    @ApiModelProperty(value = "等级数量")
    private Integer levelCount;

    @Transient
    @ApiModelProperty(value = "岗位等级列表")
    private List<PositionLevel> levels;
}
