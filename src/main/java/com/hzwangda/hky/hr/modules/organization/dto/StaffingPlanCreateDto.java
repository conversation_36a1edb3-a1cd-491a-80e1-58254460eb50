package com.hzwangda.hky.hr.modules.organization.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Min;
import java.time.LocalDate;

/**
 * 编制规划创建DTO
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Data
@ApiModel("编制规划创建DTO")
public class StaffingPlanCreateDto {

    @NotNull(message = "规划年度不能为空")
    @Min(value = 2020, message = "规划年度不能小于2020")
    @ApiModelProperty(value = "规划年度", required = true)
    private Integer planYear;

    @NotBlank(message = "机构ID不能为空")
    @ApiModelProperty(value = "机构ID", required = true)
    private String institutionId;

    @NotBlank(message = "岗位类别不能为空")
    @ApiModelProperty(value = "岗位类别", required = true)
    private String positionCategory;

    @NotNull(message = "核定编制数不能为空")
    @Min(value = 0, message = "核定编制数不能小于0")
    @ApiModelProperty(value = "核定编制数", required = true)
    private Integer approvedCount;

    @ApiModelProperty(value = "生效日期")
    private LocalDate effectiveDate;

    @ApiModelProperty(value = "结束日期")
    private LocalDate endDate;

    @ApiModelProperty(value = "人事文件ID")
    private String hrDocumentId;

    @ApiModelProperty(value = "审批状态")
    private String approvalStatus = "草稿";

    @ApiModelProperty(value = "备注")
    private String remarks;
}
