package com.hzwangda.hky.hr.modules.organization.service.impl;

import com.hzwangda.hky.hr.modules.organization.dto.*;
import com.hzwangda.hky.hr.modules.organization.entity.Institution;
import com.hzwangda.hky.hr.modules.organization.entity.InstitutionChangeLog;
import com.hzwangda.hky.hr.modules.organization.mapper.InstitutionMapper;
import com.hzwangda.hky.hr.modules.organization.repository.InstitutionRepository;
import com.hzwangda.hky.hr.modules.organization.repository.InstitutionChangeLogRepository;
import com.hzwangda.hky.hr.modules.organization.service.InstitutionService;
import com.wangda.oa.exception.EntityExistException;
import com.wangda.oa.exception.EntityNotFoundException;
import com.wangda.oa.exception.BadRequestException;
import com.wangda.oa.utils.PageUtil;
import com.wangda.oa.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 机构管理Service实现类
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(propagation = org.springframework.transaction.annotation.Propagation.SUPPORTS, readOnly = true, rollbackFor = Exception.class)
public class InstitutionServiceImpl implements InstitutionService {

    private final InstitutionRepository institutionRepository;
    private final InstitutionChangeLogRepository changeLogRepository;
    private final InstitutionMapper institutionMapper;

    @Override
    public Page<InstitutionDto> queryAll(InstitutionQueryCriteria criteria, Pageable pageable) {
        Page<Institution> page = institutionRepository.findAll((Specification<Institution>) (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (StringUtils.hasText(criteria.getInstitutionName())) {
                predicates.add(criteriaBuilder.like(root.get("institutionName"), "%" + criteria.getInstitutionName() + "%"));
            }

            if (StringUtils.hasText(criteria.getInstitutionCode())) {
                predicates.add(criteriaBuilder.like(root.get("institutionCode"), "%" + criteria.getInstitutionCode() + "%"));
            }

            if (StringUtils.hasText(criteria.getInstitutionType())) {
                predicates.add(criteriaBuilder.equal(root.get("institutionType"), criteria.getInstitutionType()));
            }

            if (criteria.getIsAdHoc() != null) {
                predicates.add(criteriaBuilder.equal(root.get("isAdHoc"), criteria.getIsAdHoc()));
            }

            if (StringUtils.hasText(criteria.getParentInstitutionId())) {
                predicates.add(criteriaBuilder.equal(root.get("parentInstitutionId"), criteria.getParentInstitutionId()));
            }

            if (StringUtils.hasText(criteria.getStatus())) {
                predicates.add(criteriaBuilder.equal(root.get("status"), criteria.getStatus()));
            }

            if (StringUtils.hasText(criteria.getLeaderId())) {
                predicates.add(criteriaBuilder.equal(root.get("leaderId"), criteria.getLeaderId()));
            }

            if (criteria.getLevel() != null) {
                predicates.add(criteriaBuilder.equal(root.get("level"), criteria.getLevel()));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        }, pageable);

        return PageUtil.toPage(page.map(institutionMapper::toDto));
    }

    @Override
    public InstitutionDto findById(String institutionId) {
        Institution institution = institutionRepository.findById(institutionId)
                .orElseThrow(() -> new EntityNotFoundException("机构不存在，ID: " + institutionId));
        
        InstitutionDto dto = institutionMapper.toDto(institution);
        
        // 如果需要包含子机构，则查询子机构
        List<Institution> children = institutionRepository.findByParentInstitutionIdOrderBySortOrder(institutionId);
        if (!children.isEmpty()) {
            dto.setChildren(children.stream().map(institutionMapper::toDto).collect(Collectors.toList()));
        }
        
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public InstitutionDto create(InstitutionCreateDto createDto) {
        log.info("创建机构开始，机构名称: {}", createDto.getInstitutionName());

        // 验证机构编码唯一性
        if (!validateInstitutionCode(createDto.getInstitutionCode(), null)) {
            throw new EntityExistException("机构编码已存在: " + createDto.getInstitutionCode());
        }

        // 验证机构名称唯一性
        if (!validateInstitutionName(createDto.getInstitutionName(), null)) {
            throw new EntityExistException("机构名称已存在: " + createDto.getInstitutionName());
        }

        // 验证父机构存在性
        if (StringUtils.hasText(createDto.getParentInstitutionId())) {
            if (!institutionRepository.existsById(createDto.getParentInstitutionId())) {
                throw new EntityNotFoundException("上级机构不存在，ID: " + createDto.getParentInstitutionId());
            }
        }

        Institution institution = institutionMapper.createDtoToEntity(createDto);
        institution.setStatus("NORMAL");
        
        Institution savedInstitution = institutionRepository.save(institution);
        
        // 记录变更日志
        recordChangeLog(savedInstitution.getInstitutionId(), "设立", 
                "新设立机构: " + savedInstitution.getInstitutionName(),
                createDto.getApprovalDocNumber(), createDto.getHrDocumentId());

        log.info("创建机构成功，机构ID: {}", savedInstitution.getInstitutionId());
        return institutionMapper.toDto(savedInstitution);
    }

    @Override
    public Boolean validateInstitutionCode(String institutionCode, String excludeId) {
        Optional<Institution> existing = institutionRepository.findByInstitutionCode(institutionCode);
        return existing.isEmpty() || (excludeId != null && existing.get().getInstitutionId().equals(excludeId));
    }

    @Override
    public Boolean validateInstitutionName(String institutionName, String excludeId) {
        Optional<Institution> existing = institutionRepository.findByInstitutionName(institutionName);
        return existing.isEmpty() || (excludeId != null && existing.get().getInstitutionId().equals(excludeId));
    }

    @Override
    public Boolean checkCircularReference(String institutionId, String parentInstitutionId) {
        if (institutionId.equals(parentInstitutionId)) {
            return true;
        }
        return institutionRepository.hasCircularReference(institutionId, parentInstitutionId);
    }

    @Override
    public List<String> getChildInstitutionIds(String institutionId) {
        return institutionRepository.findInstitutionTreeIds(institutionId);
    }

    /**
     * 记录机构变更日志
     */
    private void recordChangeLog(String institutionId, String changeType, String changeDetails,
                                String approvalDocNumber, String hrDocumentId) {
        InstitutionChangeLog changeLog = new InstitutionChangeLog();
        changeLog.setInstitutionId(institutionId);
        changeLog.setChangeType(changeType);
        changeLog.setChangeDetailsJson(changeDetails);
        changeLog.setChangeTime(LocalDateTime.now());
        changeLog.setOperatorId(SecurityUtils.getCurrentUserId());
        changeLog.setApprovalDocNumber(approvalDocNumber);
        changeLog.setHrDocumentId(hrDocumentId);
        
        changeLogRepository.save(changeLog);
    }

    // 其他方法的实现将在后续添加
    @Override
    public InstitutionDto update(InstitutionUpdateDto updateDto) {
        // TODO: 实现更新机构信息
        throw new UnsupportedOperationException("方法待实现");
    }

    @Override
    public InstitutionDto updateProperties(InstitutionPropertiesDto propertiesDto) {
        // TODO: 实现更新机构属性
        throw new UnsupportedOperationException("方法待实现");
    }

    @Override
    public InstitutionDto updateStatus(InstitutionStatusDto statusDto) {
        // TODO: 实现更新机构状态
        throw new UnsupportedOperationException("方法待实现");
    }

    @Override
    public Boolean transfer(InstitutionTransferDto transferDto) {
        // TODO: 实现机构划转
        throw new UnsupportedOperationException("方法待实现");
    }

    @Override
    public Boolean merge(InstitutionMergeDto mergeDto) {
        // TODO: 实现机构合并
        throw new UnsupportedOperationException("方法待实现");
    }

    @Override
    public List<InstitutionDto> getInstitutionTree(String rootInstitutionId) {
        // TODO: 实现查询机构树
        throw new UnsupportedOperationException("方法待实现");
    }

    @Override
    public Boolean delete(String institutionId, String deleteReason) {
        // TODO: 实现删除机构
        throw new UnsupportedOperationException("方法待实现");
    }
}
