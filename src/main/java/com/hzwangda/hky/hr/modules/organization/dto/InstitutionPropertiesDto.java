package com.hzwangda.hky.hr.modules.organization.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 机构属性更新DTO
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Data
@ApiModel("机构属性更新DTO")
public class InstitutionPropertiesDto {

    @NotBlank(message = "机构ID不能为空")
    @ApiModelProperty(value = "机构ID", required = true)
    private String institutionId;

    @ApiModelProperty(value = "机构类型")
    private String institutionType;

    @ApiModelProperty(value = "机构层级")
    private Integer level;

    @ApiModelProperty(value = "上级机构ID")
    private String parentInstitutionId;

    @ApiModelProperty(value = "负责人ID")
    private String leaderId;

    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    @ApiModelProperty(value = "办公地址")
    private String officeAddress;

    @ApiModelProperty(value = "排序号")
    private Integer sortOrder;

    @ApiModelProperty(value = "变更原因")
    private String reasonForChange;

    @ApiModelProperty(value = "批文号")
    private String approvalDocNumber;
}
