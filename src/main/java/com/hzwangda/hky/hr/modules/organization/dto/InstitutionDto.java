package com.hzwangda.hky.hr.modules.organization.dto;

import com.wangda.oa.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 机构信息DTO
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("机构信息DTO")
public class InstitutionDto extends BaseDTO {

    @ApiModelProperty(value = "机构ID")
    private String institutionId;

    @ApiModelProperty(value = "机构名称")
    private String institutionName;

    @ApiModelProperty(value = "机构编码")
    private String institutionCode;

    @ApiModelProperty(value = "机构类型")
    private String institutionType;

    @ApiModelProperty(value = "是否为非常设机构")
    private Boolean isAdHoc;

    @ApiModelProperty(value = "上级机构ID")
    private String parentInstitutionId;

    @ApiModelProperty(value = "上级机构名称")
    private String parentInstitutionName;

    @ApiModelProperty(value = "机构状态")
    private String status;

    @ApiModelProperty(value = "变更原因")
    private String reasonForChange;

    @ApiModelProperty(value = "固化数据描述")
    private String solidifiedDataDescription;

    @ApiModelProperty(value = "职责描述")
    private String responsibilities;

    @ApiModelProperty(value = "相关附件URL")
    private String attachmentUrls;

    @ApiModelProperty(value = "机构层级")
    private Integer level;

    @ApiModelProperty(value = "负责人ID")
    private String leaderId;

    @ApiModelProperty(value = "负责人姓名")
    private String leaderName;

    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    @ApiModelProperty(value = "办公地址")
    private String officeAddress;

    @ApiModelProperty(value = "排序号")
    private Integer sortOrder;

    @ApiModelProperty(value = "子机构列表")
    private List<InstitutionDto> children;

    @ApiModelProperty(value = "编制统计信息")
    private StaffingStatsDto staffingStats;
}
