package com.hzwangda.hky.hr.modules.organization.entity;

import com.wangda.oa.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 机构表实体类
 * 统一管理常设机构和非常设机构
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Entity
@Data
@EqualsAndHashCode(callSuper = true)
@Table(name = "org_institution")
@ApiModel("机构信息")
public class Institution extends BaseEntity {

    @Id
    @Column(name = "institution_id", length = 36)
    @ApiModelProperty(value = "机构ID", hidden = true)
    @GeneratedValue(generator = "uuid")
    @GenericGenerator(name = "uuid", strategy = "uuid2")
    private String institutionId;

    @Column(name = "institution_name", nullable = false, length = 200)
    @NotBlank(message = "机构名称不能为空")
    @Size(max = 200, message = "机构名称长度不能超过200个字符")
    @ApiModelProperty(value = "机构名称", required = true)
    private String institutionName;

    @Column(name = "institution_code", nullable = false, length = 50, unique = true)
    @NotBlank(message = "机构编码不能为空")
    @Size(max = 50, message = "机构编码长度不能超过50个字符")
    @ApiModelProperty(value = "机构编码", required = true)
    private String institutionCode;

    @Column(name = "institution_type", length = 50)
    @ApiModelProperty(value = "机构类型")
    private String institutionType;

    @Column(name = "is_ad_hoc")
    @ApiModelProperty(value = "是否为非常设机构")
    private Boolean isAdHoc = false;

    @Column(name = "parent_institution_id", length = 36)
    @ApiModelProperty(value = "上级机构ID")
    private String parentInstitutionId;

    @Column(name = "status", length = 20)
    @ApiModelProperty(value = "机构状态")
    private String status = "NORMAL";

    @Column(name = "reason_for_change", columnDefinition = "TEXT")
    @ApiModelProperty(value = "变更原因")
    private String reasonForChange;

    @Column(name = "solidified_data_description", columnDefinition = "TEXT")
    @ApiModelProperty(value = "固化数据描述")
    private String solidifiedDataDescription;

    @Column(name = "responsibilities", columnDefinition = "TEXT")
    @ApiModelProperty(value = "职责描述")
    private String responsibilities;

    @Column(name = "attachment_urls", columnDefinition = "TEXT")
    @ApiModelProperty(value = "相关附件URL")
    private String attachmentUrls;

    @Column(name = "level")
    @ApiModelProperty(value = "机构层级")
    private Integer level;

    @Column(name = "leader_id", length = 36)
    @ApiModelProperty(value = "负责人ID")
    private String leaderId;

    @Column(name = "contact_phone", length = 50)
    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    @Column(name = "office_address", length = 200)
    @ApiModelProperty(value = "办公地址")
    private String officeAddress;

    @Column(name = "sort_order")
    @ApiModelProperty(value = "排序号")
    private Integer sortOrder;

    @Transient
    @ApiModelProperty(value = "上级机构名称")
    private String parentInstitutionName;

    @Transient
    @ApiModelProperty(value = "负责人姓名")
    private String leaderName;
}
