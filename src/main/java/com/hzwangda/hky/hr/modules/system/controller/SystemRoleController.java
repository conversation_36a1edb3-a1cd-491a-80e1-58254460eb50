package com.hzwangda.hky.hr.modules.system.controller;

import com.hzwangda.hky.hr.modules.system.enums.SystemAuthorityEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author:
 * @Date: 2022/05/05
 * @Description: 用于操作权限
 */
@RestController
@RequiredArgsConstructor
@Api(tags = "系统管理")
@RequestMapping("/api/bpmrole")
public class SystemRoleController {

    @ApiOperation("查询权限")
    @GetMapping(value = "/authorityKey")
    public ResponseEntity<Object> getAuthorityKeys() {
        return new ResponseEntity<>(SystemAuthorityEnum.values(), HttpStatus.OK);
    }
}
