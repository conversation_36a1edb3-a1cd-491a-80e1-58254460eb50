package com.hzwangda.hky.hr.modules.organization.controller;

import com.hzwangda.hky.hr.modules.organization.dto.*;
import com.hzwangda.hky.hr.modules.organization.service.StaffingPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 编制规划Controller
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@Api(tags = "组织管理-编制管理")
@RequestMapping("/api/organizations/staffing-plans")
public class StaffingPlanController {

    private final StaffingPlanService staffingPlanService;

    @ApiOperation("查询编制规划列表")
    @GetMapping
    public ResponseEntity<Object> queryStaffingPlans(StaffingPlanQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(staffingPlanService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @ApiOperation("查询编制规划详情")
    @GetMapping("/detail")
    public ResponseEntity<Object> getStaffingPlanDetail(@RequestParam String planId) {
        return new ResponseEntity<>(staffingPlanService.findById(planId), HttpStatus.OK);
    }

    @ApiOperation("创建编制规划")
    @PostMapping("/create")
    public ResponseEntity<Object> createStaffingPlan(@Valid @RequestBody StaffingPlanCreateDto createDto) {
        return new ResponseEntity<>(staffingPlanService.create(createDto), HttpStatus.CREATED);
    }

    @ApiOperation("更新编制规划")
    @PostMapping("/update")
    public ResponseEntity<Object> updateStaffingPlan(@Valid @RequestBody StaffingPlanUpdateDto updateDto) {
        return new ResponseEntity<>(staffingPlanService.update(updateDto), HttpStatus.OK);
    }

    @ApiOperation("删除编制规划")
    @PostMapping("/delete")
    public ResponseEntity<Object> deleteStaffingPlan(@RequestParam String planId,
                                                     @RequestParam String deleteReason) {
        Boolean result = staffingPlanService.delete(planId, deleteReason);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @ApiOperation("获取当前有效编制规划")
    @GetMapping("/current")
    public ResponseEntity<Object> getCurrentEffectivePlan(@RequestParam String institutionId,
                                                          @RequestParam String positionCategory) {
        return new ResponseEntity<>(staffingPlanService.getCurrentEffectivePlan(institutionId, positionCategory), HttpStatus.OK);
    }

    @ApiOperation("验证编制规划")
    @GetMapping("/validate")
    public ResponseEntity<Object> validateStaffingPlan(@RequestParam String institutionId,
                                                       @RequestParam Integer planYear,
                                                       @RequestParam String positionCategory,
                                                       @RequestParam(required = false) String excludeId) {
        Boolean isValid = staffingPlanService.validateStaffingPlan(institutionId, planYear, positionCategory, excludeId);
        return new ResponseEntity<>(isValid, HttpStatus.OK);
    }
}
