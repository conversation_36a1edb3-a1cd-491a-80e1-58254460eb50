#配置数据源
spring:
  datasource:
    druid:
      db-type: com.alibaba.druid.pool.DruidDataSource
      name: DataSource-system-MS
      driverClassName: com.mysql.cj.jdbc.Driver
      url: jdbc:mysql://${MYSQL_HOST}/${MYSQL_DATABASE}?serverTimezone=GMT%2B8&characterEncoding=utf8&useSSL=false&nullCatalogMeansCurrent=true
      username: ${MYSQL_USERNAME}
      password: ${MYSQL_PASSWORD}
      # 初始连接数
      initial-size: 5
      # 最小连接数
      min-idle: 10
      # 最大连接数
      max-active: 20
      # 获取连接超时时间
      max-wait: 5000
      # 连接有效性检测时间
      time-between-eviction-runs-millis: 60000
      # 连接在池中最小生存的时间
      min-evictable-idle-time-millis: 300000
      # 连接在池中最大生存的时间
      max-evictable-idle-time-millis: 900000
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      # 检测连接是否有效
      validation-query: select 1 from dual
      # 配置监控统计
      web-stat-filter:
        enabled: true
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
        # 登录名密码
        login-username: xxxAdmin
        login-password: adminpw123
      filter:
        stat:
          enabled: true
          # 记录慢SQL
          log-slow-sql: true
          slow-sql-millis: 1500
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true

  redis:
    host: ${REDIS_HOST}
    #数据库索引
    database: ${REDIS_DB}
    port: ${REDIS_PORT}
    password: ${REDIS_PWD}

fenix:
  debug: false

# 登录相关配置
login:
  # 登录缓存
  user-cache:
    idle-time: 14400
  #  是否限制单用户登录
  single: false
  #  验证码
  login-code:
    #  验证码类型配置 查看 LoginProperties 类
    code-type: spec
    #  登录图形验证码有效时间/分钟
    expiration: 2
    #  验证码高度
    width: 111
    #  验证码宽度
    heigth: 36
    # 内容长度
    length: 2
    # 字体名称，为空则使用默认字体
    font-name:
    # 字体大小
    font-size: 25

#jwt
jwt:
  header: Authorization
  # 令牌前缀
  token-start-with: Bearer
  # 必须使用最少88位的Base64对该令牌进行编码
  base64-secret: ZmQ0ZGI5NjQ0MDQwY2I4MjMxY2Y3ZmI3MjdhN2ZmMjNhODViOTg1ZGE0NTBjMGM4NDA5NzYxMjdjOWMwYWRmZTBlZjlhNGY3ZTg4Y2U3YTE1ODVkZDU5Y2Y3OGYwZWE1NzUzNWQ2YjFjZDc0NGMxZWU2MmQ3MjY1NzJmNTE0MzI=
  # 令牌过期时间 此处单位/毫秒 ，默认4小时，可在此网站生成 https://www.convertworld.com/zh-hans/time/milliseconds.html
  token-validity-in-seconds: 14400000
  # 在线用户key
  online-key: online-token-
  # 验证码
  code-key: code-key-
  # token 续期检查时间范围（默认30分钟，单位毫秒），在token即将过期的一段时间内用户操作了，则给用户的token续期
  detect: 1800000
  # 续期时间范围，默认1小时，单位毫秒
  renew: 3600000

#是否允许生成代码，生产环境设置为false
generator:
  enabled: false

#是否开启 swagger-ui
swagger:
  enabled: false

# IP 本地解析
ip:
  local-parsing: true

# 文件存储路径
file:
  linux:
    path: /usr/wangda/storage/file/
    avatar: /usr/wangda/storage/avatar/
  windows:
    path: C:\wangda\storage\file\
    avatar: C:\wangda\storage\avatar\
  # 文件大小 /M
  maxSize: 100
  avatarMaxSize: 5
  url: file
  # 模式：localStorageServiceImpl、oneStorServiceImpl
  storageMode: localStorageServiceImpl
  # 允许上传的后缀名列表(安全白名单)，若不配置表示不启用
  suffixAllowlist:
  # 不允许上传的后缀名列表(安全黑名单)
  suffixBlocklist: jsp,class,jar,php,php3,pl,asp,aspx,ascx,cfm,cfc,vbs,js,sh,bat,cmd,reg,exe,dll,com

url:
  #服务地址
  serverUrl: http://hr.hzwangda.com/hr-service
