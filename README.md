<h1 style="text-align: center">杭州科技职业技术学院人事管理系统</h1>

#### 项目简介

一个基于 Spring Boot 2.6 、 Spring Boot Jpa、 JWT、Spring Security、Redis、Vue的前后端分离的人事管理系统

#### 其他依懒

- 使用lombok，开发工具需要导入lombok插件

**账号密码：** `admin / 123456`

- `hr-service` 服务，端口：8000

#### 中间件

- `redis` dbx

#### 项目结构

项目采用按功能模块分，结构如下

- `system` 为系统设置

- 模块
    - `employee` 员工管理
    - `department` 部门管理
    - `position` 职位管理
    - `attendance` 考勤管理
    - `salary` 薪资管理

- `util` 工具相关

#### 详细结构

```
- 表名规范：模块名第一个字母_类名，例在employee模块下的EmployeeInfo类的表名：e_employee_info
```
    
