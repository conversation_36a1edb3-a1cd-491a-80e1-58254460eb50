# Augment AI 使用指南

## 项目概述

该JAVA工程是一个基于Spring Boot 2.6的java快速开发框架，采用前后端分离架构，通过jar集成了工作流引擎、在线办公、文档管理等核心功能。项目基于基础功能开发业务模块，如考勤管理、薪资管理等业务功能。

### 核心技术栈
- **后端**: Spring Boot 2.1.18, Spring Security, Spring Data JPA, Flowable工作流引擎
- **数据库**: MySQL/Oracle双数据库支持
- **缓存**: Redis (db7)
- **文档处理**: WPS在线编辑、金格中间件
- **存储**: 阿里云OSS对象存储

## 项目架构说明

### 模块结构
```
aigov-service/
├── bf/                          # 基础框架模块
│   ├── bf-common/              # 公共模块(工具类、配置、异常处理)
│   ├── bf-system/              # 系统核心模块(用户管理、权限、监控)
│   ├── bf-logging/             # 日志模块
│   ├── bf-tools/               # 第三方工具模块
│   └── bf-generator/           # 代码生成模块
├── aigov-workflow/             # 工作流引擎模块
├── aigov-storage-oss/          # OSS存储服务模块
├── aigov-zzkk-database/        # 中正科技数据库模块
├── aigov-officeonline/         # 在线办公模块
└── project-demo/               # 业务演示模块，如hr-service(src/main/java/com/hzwangda/hky/hr/),(主要启动入口)
│   ├── AppRun.java             # 主启动类
│   └── modules/                # 业务模块
│       ├── system/             # 系统管理模块(用户管理、权限、监控)
│       ├── employee/           # 员工管理模块
│       ├── department/         # 部门管理模块
│       ├── position/           # 职位管理模块
│       ├── attendance/         # 考勤管理模块
│       └── salary/             # 薪资管理模块
├── src/main/resources/         # 配置文件
└── pom.xml                     # Maven配置文件
```

### 公共功能域
- **用户权限管理**: 基于RBAC的权限控制体系
- **工作流管理**: 基于Flowable的流程引擎
- **公文管理**: 政务公文流转处理
- **在线办公**: WPS/金格在线编辑集成
- **文档存储**: OSS云存储服务
- **系统监控**: 服务器性能监控
- **代码生成**: 基于模板的代码自动生成

## 开发规范与约定

### 代码规范
- **Java**: 遵循阿里巴巴Java开发手册
- **数据库**: 表名使用下划线命名，字段名驼峰命名
- **API**: RESTful风格，统一返回格式，**仅使用GET和POST方法**
- **HTTP方法**: 只能使用GET和POST，避免使用DELETE、PUT等方法
- **文档**: 使用Swagger进行API文档管理

### Java代码编写习惯

#### 类和接口命名
- **Entity类**: 与数据表对应，使用名词，如 `Employee`、`Department`、`Position`
- **Controller类**: 以Controller结尾，如 `EmployeeController`、`DepartmentController`
- **Service接口**: 以Service结尾，如 `EmployeeService`、`DepartmentService`
- **Service实现类**: 以ServiceImpl结尾，如 `EmployeeServiceImpl`
- **Repository接口**: 以Repository结尾，如 `EmployeeRepository`
- **DTO类**: 以Dto结尾，如 `EmployeeDto`、`DepartmentDto`
- **VO类**: 以Vo结尾，如 `EmployeeVo`、`DepartmentVo`
- **查询条件类**: 以QueryCriteria结尾，如 `EmployeeQueryCriteria`

#### 方法命名规范
- **查询方法**: `findById`、`findByName`、`queryList`、`getDetail`
- **保存方法**: `save`、`create`、`update`、`saveOrUpdate`
- **删除方法**: `delete`、`deleteById`、`remove`
- **验证方法**: `validate`、`check`、`verify`
- **转换方法**: `convert`、`transform`、`toDto`、`toEntity`

#### HTTP方法使用规范
- **GET方法**: 用于查询操作，如列表查询、详情查询、导出等
- **POST方法**: 用于所有修改操作，包括新增、更新、删除等
- **禁用方法**: 不使用PUT、DELETE、PATCH等HTTP方法
- **参数传递**: 尽量避免使用路径参数(@PathVariable)，优先使用请求参数(@RequestParam)或请求体(@RequestBody)

#### 注解使用规范
```java
// Controller层 - 标准HTTP方法使用
@RestController
@RequiredArgsConstructor
@Api(tags = "员工管理")
@RequestMapping("/api/employee")
@Slf4j
public class EmployeeController {

    @ApiOperation("查询员工列表")
    @GetMapping
    @PreAuthorize("@el.check('employee:list')")
    public ResponseEntity<Object> query(EmployeeQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(employeeService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @ApiOperation("根据ID查询员工详情")
    @GetMapping("/detail")
    @PreAuthorize("@el.check('employee:list')")
    public ResponseEntity<Object> findById(@RequestParam Long id) {
        return new ResponseEntity<>(employeeService.findById(id), HttpStatus.OK);
    }

    @ApiOperation("创建员工")
    @PostMapping
    @PreAuthorize("@el.check('employee:add')")
    public ResponseEntity<Object> create(@Valid @RequestBody Employee resources) {
        return new ResponseEntity<>(employeeService.create(resources), HttpStatus.CREATED);
    }

    @ApiOperation("修改员工")
    @PostMapping("/update")
    @PreAuthorize("@el.check('employee:edit')")
    public ResponseEntity<Object> update(@Valid @RequestBody Employee resources) {
        employeeService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @ApiOperation("删除员工")
    @PostMapping("/delete")
    @PreAuthorize("@el.check('employee:del')")
    public ResponseEntity<Object> delete(@RequestBody Set<Long> ids) {
        employeeService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}

// Service层
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true, rollbackFor = Exception.class)
@Slf4j
public class EmployeeServiceImpl implements EmployeeService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public EmployeeDto create(Employee resources) {
        // 实现逻辑
    }
}

// Entity层
@Entity
@Data
@Table(name = "hr_employee")
@ApiModel("员工")
public class Employee extends BaseEntity {

    @Id
    @Column(name = "employee_id")
    @ApiModelProperty(value = "ID", hidden = true)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "employee_no", nullable = false, unique = true)
    @ApiModelProperty(value = "员工编号")
    private String employeeNo;

    @Column(name = "name", nullable = false)
    @ApiModelProperty(value = "姓名")
    private String name;
}
```

#### 异常处理规范
```java
// 自定义业务异常
@Service
public class EmployeeServiceImpl implements EmployeeService {

    @Override
    public EmployeeDto findById(Long id) {
        Employee employee = employeeRepository.findById(id)
            .orElseThrow(() -> new EntityNotFoundException("员工不存在，ID: " + id));
        return employeeMapper.toDto(employee);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public EmployeeDto create(Employee resources) {
        if (employeeRepository.findByEmployeeNo(resources.getEmployeeNo()) != null) {
            throw new EntityExistException("员工编号已存在");
        }
        return employeeMapper.toDto(employeeRepository.save(resources));
    }
}

// 全局异常处理
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(EntityNotFoundException.class)
    public ResponseEntity<ApiError> handleEntityNotFound(EntityNotFoundException e) {
        return buildResponseEntity(ApiError.error(e.getMessage()));
    }
}
```

#### 日志记录规范
```java
@Slf4j
@Service
public class EmployeeServiceImpl implements EmployeeService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public EmployeeDto create(Employee resources) {
        log.info("创建员工开始，员工编号: {}", resources.getEmployeeNo());

        try {
            EmployeeDto result = employeeMapper.toDto(employeeRepository.save(resources));
            log.info("创建员工成功，员工ID: {}", result.getId());
            return result;
        } catch (Exception e) {
            log.error("创建员工失败，员工编号: {}, 错误信息: {}", resources.getEmployeeNo(), e.getMessage(), e);
            throw e;
        }
    }
}
```

#### 参数验证规范
```java
// DTO验证
@Data
@ApiModel("员工创建DTO")
public class EmployeeCreateDto {

    @NotBlank(message = "员工编号不能为空")
    @Size(min = 3, max = 20, message = "员工编号长度必须在3-20个字符之间")
    @ApiModelProperty(value = "员工编号", required = true)
    private String employeeNo;

    @NotBlank(message = "姓名不能为空")
    @Size(min = 2, max = 10, message = "姓名长度必须在2-10个字符之间")
    @ApiModelProperty(value = "姓名", required = true)
    private String name;

    @Email(message = "邮箱格式不正确")
    @ApiModelProperty(value = "邮箱")
    private String email;
}

// Controller参数验证 - 使用POST方法
@PostMapping("/create")
@ApiOperation("创建员工")
public ResponseEntity<Object> create(@Valid @RequestBody EmployeeCreateDto employeeDto) {
    return new ResponseEntity<>(employeeService.create(employeeDto), HttpStatus.CREATED);
}

// 查询操作使用GET方法，避免路径参数
@GetMapping("/detail")
@ApiOperation("根据ID查询员工")
public ResponseEntity<Object> findById(@RequestParam Long id) {
    return new ResponseEntity<>(employeeService.findById(id), HttpStatus.OK);
}

// 删除操作使用POST方法，通过请求体传递参数
@PostMapping("/delete")
@ApiOperation("删除员工")
public ResponseEntity<Object> delete(@RequestBody Map<String, Long> params) {
    Long id = params.get("id");
    employeeService.deleteById(id);
    return new ResponseEntity<>(HttpStatus.OK);
}

// 或者删除多个员工
@PostMapping("/deleteByIds")
@ApiOperation("批量删除员工")
public ResponseEntity<Object> deleteByIds(@RequestBody Set<Long> ids) {
    employeeService.deleteAll(ids);
    return new ResponseEntity<>(HttpStatus.OK);
}
```

#### 数据库操作规范
```java
// Repository层
@Repository
public interface EmployeeRepository extends JpaRepository<Employee, Long>, JpaSpecificationExecutor<Employee> {

    /**
     * 根据员工编号查找员工
     * @param employeeNo 员工编号
     * @return 员工实体
     */
    Employee findByEmployeeNo(String employeeNo);

    /**
     * 根据邮箱查找员工
     * @param email 邮箱
     * @return 员工实体
     */
    Optional<Employee> findByEmail(String email);

    /**
     * 查询在职状态的员工
     * @param status 在职状态
     * @return 员工列表
     */
    List<Employee> findByStatus(String status);
}

// 复杂查询使用Specification
@Service
public class EmployeeServiceImpl implements EmployeeService {

    @Override
    public Object queryAll(EmployeeQueryCriteria criteria, Pageable pageable) {
        Page<Employee> page = employeeRepository.findAll((root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (StringUtils.isNotBlank(criteria.getName())) {
                predicates.add(criteriaBuilder.like(root.get("name"), "%" + criteria.getName() + "%"));
            }

            if (StringUtils.isNotBlank(criteria.getStatus())) {
                predicates.add(criteriaBuilder.equal(root.get("status"), criteria.getStatus()));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        }, pageable);

        return PageUtil.toPage(page.map(employeeMapper::toDto));
    }
}
```

#### MapStruct映射规范
```java
// Mapper接口
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface EmployeeMapper extends BaseMapper<EmployeeDto, Employee> {

    /**
     * 转换为DTO
     * @param entity 实体
     * @return DTO
     */
    @Override
    EmployeeDto toDto(Employee entity);

    /**
     * 转换为实体
     * @param dto DTO
     * @return 实体
     */
    @Override
    Employee toEntity(EmployeeDto dto);

    /**
     * 批量转换为DTO
     * @param entityList 实体列表
     * @return DTO列表
     */
    List<EmployeeDto> toDto(List<Employee> entityList);

    /**
     * 创建时的映射，忽略ID和审计字段
     * @param createDto 创建DTO
     * @return 实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    Employee createDtoToEntity(EmployeeCreateDto createDto);
}
```

## 通用查询

本项目对 Jpa 的查询进行了封装，现可以通过 `@Query` 注解实现简单的查询与复杂查询

简单查询：`等于(默认)、大于等于、小于等于、左模糊、右模糊、中模糊、多字段模糊、NOT_EQUAL 、BETWEEN 、NOT_NULL`。

复杂查询：`包含（IN）查询、左连接、右连接等`

## 参数说明

| 字段名称 | 字段描述                                             | 默认值 |
| -------- | ---------------------------------------------------- | ------ |
| propName | 对象的属性名，如果字段名称与实体字段一致，则可以省略 | ""     |
| type     | 查询方式，默认为                                     | EQUAL  |
| blurry   | 多字段模糊查询，值为实体字段名称                     | ""     |
| joinName | 关联实体的名称                                       | ""     |
| join     | 连接查询方式，左连接或者右连接                       | LEFT   |

## 使用方式
**1、创建一个查询类 `QueryCriteria`**

``` java
@Data
public class QueryCriteria {

    // 等于
    @Query
    private String a;

    // 左模糊
    @Query(type = Query.Type.LEFT_LIKE)
    private String b;

    // 右模糊
    @Query(type = Query.Type.RIGHT_LIKE)
    private String c;

    // 大于等于
    @Query(type = Query.Type.GREATER_THAN, propName = "createTime")
    private Timestamp startTime;

    // 小于等于
    @Query(type = Query.Type.LESS_THAN, propName = "createTime")
    private Timestamp endTime;

    // BETWEEN
    @Query(type = Query.Type.BETWEEN)
    private List<Timestamp> startTime;

    // 多字段模糊查询，blurry 为字段名称
    @Query(blurry = "a,b,c")
    private String blurry;

    // IN 查询
    @Query(type = Query.Type.IN)
    private List<String> d;

    // 左关联查询，left Join ， joinName为关联实体名称 , propName为关联实体 字段
    @Query(joinName = "", propName="")
    private String e;

    // 右关联查询，right Join ， joinName为关联实体名称 , propName为关联实体 字段
    @Query(joinName = "", propName="", join = Query.Join.RIGHT)
    private String f;

    // NOT_EQUAL 不等于
    @Query(type = Query.Type.NOT_EQUAL)
    private String g;

    // NOT_NULL 不为空
    @Query(type = Query.Type.NOT_NULL)
    private String g;
}
```

关联查询参考：`DictDetailQueryCriteria` 类，下面代码为关联查询 dict 表里面的 name 字段

```java
public class DictDetailQueryCriteria {
    @Query(propName = "name",joinName = "dict")
    private String dictName;
}

// propName = "name" 为关联实体 Dict 中的字段名称
public class Dict extends BaseEntity implements Serializable {
    @NotBlank
    @ApiModelProperty(value = "名称")
    private String name;
}

// joinName = "dict" 为关联实体名称
public class DictDetail extends BaseEntity implements Serializable {
    @JoinColumn(name = "dict_id")
    @ManyToOne(fetch=FetchType.LAZY)
    @ApiModelProperty(value = "字典", hidden = true)
    private Dict dict;
}
```

**2、在控制器中使用**

```java
// Pageable 分页查询
public ResponseEntity query(QueryCriteria criteria, Pageable pageable){
    return new ResponseEntity(service.queryAll(criteria,pageable), HttpStatus.OK);
}
```
**3、Service 中查询**

```
@Override
public Object queryAll(QueryCriteria criteria, Pageable pageable){
    Page<实体> page = repository.findAll(((root, criteriaQuery, cb) -> QueryHelp.getPredicate(root, criteria, cb)),pageable);
    return page;
}
```
**tip**

如果需要添加一个字段查询，只需要在查询类 `QueryCriteria` 中添加就可以了，可节省大量时间。

源码可以查看 `bf-common` 模块中的 `com.hzwangda.bf.annotation.Query` 与 `com.hzwangda.bf.utils.QueryHelp`



#### API参数传递规范
```java
// ✅ 推荐的参数传递方式

// GET请求 - 使用@RequestParam
@GetMapping("/list")
@ApiOperation("查询员工列表")
public ResponseEntity<Object> getEmployeeList(
    @RequestParam(required = false) String name,
    @RequestParam(required = false) String status,
    @RequestParam(defaultValue = "0") Integer page,
    @RequestParam(defaultValue = "20") Integer size) {
    // 实现逻辑
}

@GetMapping("/detail")
@ApiOperation("查询员工详情")
public ResponseEntity<Object> getEmployeeDetail(@RequestParam Long id) {
    return new ResponseEntity<>(employeeService.findById(id), HttpStatus.OK);
}

// POST请求 - 使用@RequestBody
@PostMapping("/create")
@ApiOperation("创建员工")
public ResponseEntity<Object> createEmployee(@RequestBody EmployeeCreateDto employeeDto) {
    return new ResponseEntity<>(employeeService.create(employeeDto), HttpStatus.CREATED);
}

@PostMapping("/update")
@ApiOperation("更新员工")
public ResponseEntity<Object> updateEmployee(@RequestBody EmployeeUpdateDto employeeDto) {
    employeeService.update(employeeDto);
    return new ResponseEntity<>(HttpStatus.OK);
}

@PostMapping("/delete")
@ApiOperation("删除员工")
public ResponseEntity<Object> deleteEmployee(@RequestBody Map<String, Object> params) {
    Long id = Long.valueOf(params.get("id").toString());
    employeeService.deleteById(id);
    return new ResponseEntity<>(HttpStatus.OK);
}

// ❌ 避免使用的方式
// @GetMapping("/detail/{id}")  // 避免路径参数
// @DeleteMapping("/delete/{id}")  // 避免DELETE方法
// @PutMapping("/update/{id}")  // 避免PUT方法
```

### 安全要求
- 所有API接口需要权限验证
- 文件上传需要类型和大小限制
- 敏感数据需要加密存储
- SQL注入防护
- 使用@PreAuthorize进行方法级权限控制

#### 工具类编写规范
```java
// 工具类示例
@UtilityClass
@Slf4j
public class FileUtils {

    /**
     * 获取文件扩展名
     * @param filename 文件名
     * @return 扩展名
     */
    public static String getExtension(String filename) {
        if (StringUtils.isBlank(filename)) {
            return "";
        }
        int lastDotIndex = filename.lastIndexOf('.');
        return lastDotIndex > 0 ? filename.substring(lastDotIndex + 1).toLowerCase() : "";
    }

    /**
     * 验证文件类型
     * @param filename 文件名
     * @param allowedTypes 允许的类型
     * @return 是否允许
     */
    public static boolean isAllowedType(String filename, Set<String> allowedTypes) {
        String extension = getExtension(filename);
        return allowedTypes.contains(extension);
    }
}
```

#### 常量定义规范
```java
// 常量类
public final class Constants {

    private Constants() {
        throw new IllegalStateException("Utility class");
    }

    // 系统常量
    public static final String SYSTEM_USER = "system";
    public static final String DEFAULT_PASSWORD = "123456";

    // 缓存相关
    public static final String CACHE_USER = "user";
    public static final String CACHE_MENU = "menu";
    public static final int CACHE_EXPIRE_TIME = 3600;

    // 文件相关
    public static final long MAX_FILE_SIZE = 10 * 1024 * 1024L; // 10MB
    public static final Set<String> ALLOWED_IMAGE_TYPES = Set.of("jpg", "jpeg", "png", "gif");
    public static final Set<String> ALLOWED_DOCUMENT_TYPES = Set.of("doc", "docx", "pdf", "txt");
}
```

### 测试要求
- 单元测试覆盖率不低于70%
- 集成测试覆盖核心业务流程
- 性能测试关注响应时间和并发能力
- 使用@SpringBootTest进行集成测试
- 使用@MockBean进行依赖模拟

#### 单元测试示例
```java
@ExtendWith(MockitoExtension.class)
class EmployeeServiceImplTest {

    @Mock
    private EmployeeRepository employeeRepository;

    @Mock
    private EmployeeMapper employeeMapper;

    @InjectMocks
    private EmployeeServiceImpl employeeService;

    @Test
    @DisplayName("根据ID查找员工 - 成功")
    void findById_Success() {
        // Given
        Long employeeId = 1L;
        Employee employee = new Employee();
        employee.setId(employeeId);
        employee.setEmployeeNo("EMP001");
        employee.setName("张三");

        EmployeeDto employeeDto = new EmployeeDto();
        employeeDto.setId(employeeId);
        employeeDto.setEmployeeNo("EMP001");
        employeeDto.setName("张三");

        when(employeeRepository.findById(employeeId)).thenReturn(Optional.of(employee));
        when(employeeMapper.toDto(employee)).thenReturn(employeeDto);

        // When
        EmployeeDto result = employeeService.findById(employeeId);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(employeeId);
        assertThat(result.getName()).isEqualTo("张三");

        verify(employeeRepository).findById(employeeId);
        verify(employeeMapper).toDto(employee);
    }

    @Test
    @DisplayName("根据ID查找员工 - 员工不存在")
    void findById_EmployeeNotFound() {
        // Given
        Long employeeId = 999L;
        when(employeeRepository.findById(employeeId)).thenReturn(Optional.empty());

        // When & Then
        assertThatThrownBy(() -> employeeService.findById(employeeId))
            .isInstanceOf(EntityNotFoundException.class)
            .hasMessage("员工不存在，ID: " + employeeId);

        verify(employeeRepository).findById(employeeId);
        verifyNoInteractions(employeeMapper);
    }
}
```

## 常用开发场景

### 场景1: 新增业务模块
1. 设计数据库表结构
2. 使用代码生成器生成基础CRUD代码
3. 编写业务逻辑Service层
4. 配置权限和API接口
5. 编写单元测试和集成测试

### 场景2: 人事业务流程开发
1. 设计员工入职流程
2. 配置考勤规则和计算逻辑
3. 实现薪资计算和发放流程
4. 开发绩效考核管理功能
5. 集成邮件通知和消息提醒

### 场景3: 第三方系统集成
1. 分析接口文档和认证方式
2. 封装HTTP客户端调用
3. 实现数据格式转换
4. 添加异常处理和重试机制

## 注意事项

### 开发环境要求
- JDK 1.8+
- Maven 3.6+
- Redis 5.0+
- MySQL 8.0+
- Lombok插件(IDE必须安装)

### 部署相关
- 使用Docker容器化部署
- 配置文件区分环境(dev/test/prod)
- 日志文件统一管理
- 监控告警配置

### 性能考虑
- 接口响应时间监控
