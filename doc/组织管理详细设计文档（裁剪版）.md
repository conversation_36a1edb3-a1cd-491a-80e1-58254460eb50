# **组织管理模块详细设计文档**

文档版本: V2.5

编制日期: 2025年8月6日

## **1\. 引言**

### **1.1. 目的**

本文档是‘杭州科技职业技术学院人事管理系统软件规格说明书’的‘组织管理’分册，旨在作为该模块设计、开发、测试和验收的依据。它详细说明了模块的功能、数据模型、业务规则及API接口规范，为所有项目相关方提供一个统一、清晰的实现蓝图。

### **1.2. 范围**

本模块详细设计涵盖对学校内设机构（包括常设机构和非常设机构）从设立、修改、划转、撤销到合并的全生命周期管理。同时，也包括编制规划与控制、人事文件管理（包含编制文件）、机构历史信息查询与统计、以及与下游系统的数据集成。本模块是人事管理系统的核心基础，其数据的准确性和有效性将直接影响到教职工信息管理、人事变动、薪酬核算、绩效考核、招聘管理等所有下游业务模块。因此，本模块的设计必须非常仔细、考虑周全，并确保与信息中心等相关部门的平滑对接，以保障全校数据的准确性和一致性。

### **1.3. 预期读者**

项目经理：用于宏观掌握项目进度和资源分配。

系统架构师：用于理解模块设计，确保其与整体系统架构的兼容性、可扩展性和健壮性。

开发团队：作为日常开发工作最直接的参考，指导具体的代码实现。

测试团队：用于制定全面的测试用例，确保功能与文档描述一致。

人事处业务人员（最终用户）：用于理解系统将如何满足其日常工作需求。

校领导：用于审查和批准系统设计，确保其符合学校的战略规划。

## **2\. 功能需求与用户故事**

### **2.1. 核心功能概述**

本模块的核心用户角色为“人事处管理员”，其核心功能旨在提供一个全面、直观的组织架构管理平台。主要功能包括：

* **组织架构维护**: 对学校各级机构进行增、删、改、查、合并、划转、停用等操作，并以树状结构直观展示。支持非常设机构的标识和管理。  
* **部门属性维护**: 设置和维护部门的类型、层级、隶属关系、负责人等属性。  
* **组织合并**: 支持多机构合并操作，确保数据迁移的完整性和一致性。  
* **组织划转**: 将机构及其下属子机构和人员从原上级机构划转到新的上级机构。  
* **[规划功能，暂不实现] 导出与导入**: 支持组织架构数据的导出和导入功能，提高数据管理效率。  
* **[规划功能，暂不实现] 架构图**: 以图形化方式展示组织架构，支持缩放、打印和导出。  
* **组织沿革**: 创建组织快照，提供版本回溯和比较功能。  
* **编制管理**: 管理各机构的核定编制数，支持按岗位类别细分，并能自动计算在编、超编和缺编数。  
* **岗位序列管理**: 预设管理岗、专技岗、工勤岗、其他等岗位序列，岗位序列下设若干岗位等级。  
* **人事文件管理**: 集中管理各类人事相关文件，包括编制文件、岗位设置文件等，并支持灵活引用。  
* **数据固化与追溯**: 确保机构变更时历史数据的准确性和可追溯性。  
* **[规划功能，暂不实现] 查询与统计**: 提供复合查询功能和报表生成，支持管理决策和业务分析。

### **2.2. 用户故事与验收标准**

* **故事1：以树状图维护组织架构** \* **作为**: 人事处管理员  
  * **我希望**: 在系统中以图形化树状结构创建、查看、修改和停用学校的各级组织（学院、部门、非常设机构等）  
  * **以便于**: 我能准确、直观地维护学校的官方组织架构，快速定位和管理任何级别的机构，并随时了解机构间的层级关系。  
  * **验收标准**:  
    1. 系统必须能够展示完整的机构层级关系，支持无限级数的机构嵌套。  
    2. 管理员可以对机构进行新增、编辑、停用等操作，并且操作结果应在树状图中实时动态更新，提供良好的交互体验。  
    3. 系统应支持对非常设机构的独立管理，在树状图中能通过特殊的图标或颜色进行区分，但其数据模型与常设机构统一。  
    4. 在创建或修改机构时，系统需进行严格的数据校验，确保机构名称和机构编码在同一父级机构下是唯一的。  
* **故事2：机构划转** \* **作为**: 人事处管理员  
  * **我希望**: 将某个机构及其下属所有子机构和人员，从原上级机构划转到新的上级机构下  
  * **以便于**: 我可以灵活地调整组织架构，以适应学校管理需要，例如部门合并、职能调整等场景。  
  * **验收标准**:  
    1. 划转操作应支持单机构和包含子机构的整体划转，且划转过程需确保数据完整性。  
    2. 系统应在操作完成后自动生成一条详细的划转日志，该日志应包含被划转机构ID、操作前后的父机构信息、操作人ID及操作时间。  
    3. 机构划转后，系统应自动触发员工信息更新，将该机构及其所有下属机构下的员工的“所属机构”信息更新到新的父机构之下。  
    4. 划转操作应在后台以事务方式执行，确保数据的一致性，如果任何步骤失败，则整个操作回滚。  
* **故事3：机构变更时固化历史数据** \* **作为**: 人事处管理员  
  * **我希望**: 在机构被改名或撤并后，系统能够自动固化关联人员的历史任职记录，并保留当时的机构名称  
  * **以便于**: 我能在查询历史数据时，看到的是当时真实的机构名称，而不是变更后的新名称，以保证数据的准确性和可追溯性，满足审计和历史研究的需求。  
  * **验收标准**:  
    1. 当一个机构名称从“A”改为“B”后，查询某员工在该机构的历史任职记录，显示的机构名仍为“A”。  
    2. 当机构“C”被合并入机构“D”后，原“C”机构员工的历史任职记录中，机构名仍显示为“C”，并附带标识说明其已合并。  
    3. 所有的机构变更操作（改名、撤并、划转）都必须生成一条详细的变更日志，记录变更前后的数据，并存储于org\_institution\_change\_log表中，并可关联到相应的“人事文件”作为参考依据。  
    4. 对于已固化的历史记录，其关联的机构应有明确的标识（例如solidified\_data\_description字段），指出该机构名称为历史名称，方便前端进行展示和区分。  
* **故事4：手动设置机构编制信息** \* **作为**: 人事处管理员  
  * **我希望**: 为每一个组织机构手动设置和更新其核定编制数（可按管理岗、专技岗等细分）  
  * **以便于**: 我可以对全校的编制使用情况进行精细化管理和实时监控，为人员招聘和岗位调整提供数据支持。  
  * **验收标准**:  
    1. 在每个机构的管理界面，都有明确的字段用于录入核定编制数，支持按岗位类别进行细分。  
    2. 系统能根据该机构下的在编人员数（从emp\_employee表获取），自动计算并实时显示“在编人数”、“超编数”和“缺编数”，并在超编或缺编时给出醒目的提示。
* **故事5：管理人事文件** \* **作为**: 人事处管理员  
  * **我希望**: 能够集中管理学校的各类人事相关文件，并支持灵活引用  
  * **以便于**: 我可以确保编制规划和组织变更的依据清晰、统一，方便追溯和管理。  
  * **验收标准**:  
    1. 系统提供独立的人事文件管理界面，支持上传、下载、查看、编辑（元数据）和删除人事文件。  
    2. 人事文件本身不需要版本管理，每次上传视为一份独立的文件。  
    3. 编制规划和组织变更在创建或更新时，可以灵活选择引用已上传的**人事文件**。  
    4. 系统能记录人事文件的上传人、上传时间、文件类型等元数据。  
* **新增故事：组织沿革** \* **作为**: 人事处管理员  
  * **我希望**: 能够查看任意机构从设立至今的所有变更历史，包括名称变更、上下级调整、合并、撤销等，并能清晰地了解每个阶段的机构状态。  
  * **以便于**: 我可以全面了解机构的发展历程，为历史数据分析和决策提供依据。  
  * **验收标准**:  
    1. 系统提供机构沿革查询功能，输入机构ID或名称即可查询其完整历史。  
    2. 沿革记录应按时间线展示，包含变更类型、变更详情、变更时间、操作人等关键信息。  
    3. 每条沿革记录应能关联到相应的**人事文件**作为参考依据。  
    4. 系统能够展示机构在不同历史时间点的层级关系和状态。

## **3\. 数据库设计**

### **3.1. 数据库表结构**

#### **3.1.1. 机构表 (org\_institution)**

该表统一管理常设机构和非常设机构，新增is\_ad\_hoc字段进行区分。

* **institution\_id**: 主键，VARCHAR(36)，系统自动生成。  
* **institution\_name**: 机构名称，VARCHAR(200)，唯一索引保证名称不重复。  
* **institution\_code**: 机构编码，VARCHAR(50)，唯一索引。  
* **institution\_type**: 机构类型，VARCHAR(50)，如COLLEGE、DEPARTMENT。  
* **is\_ad\_hoc**: 是否为非常设机构，BOOLEAN。  
* **parent\_institution\_id**: 上级机构ID，VARCHAR(36)，外键，关联自身表。  
* **status**: 机构状态，VARCHAR(20)，字典项：NORMAL、DISABLED、WITHDRAWN等。  
* **reason\_for\_change**: 变更原因，TEXT。  
* **solidified\_data\_description**: 固化数据描述，TEXT，用于记录历史数据固化的详情，例如：机构A在2025年5月1日更名为机构B，本字段会记录"原机构名称：机构A"等信息。  
* **responsibilities**: 职责描述，TEXT，原非常设机构字段。  
* **attachment\_urls**: 相关附件URL，TEXT，原非常设机构字段，以逗号分隔存储多个链接。

#### **3.1.2. 编制规划表 (org\_staffing\_plan)**

* **plan\_id**: 主键，VARCHAR(36)，系统自动生成。  
* **plan\_year**: 规划年度，INT。  
* **institution\_id**: 机构ID，VARCHAR(36)，外键，关联org\_institution表。  
* **position\_category**: 岗位类别，VARCHAR(50)，字典项：管理岗、专技岗等。  
* **approved\_count**: 核定编制数，INT。  
* **effective\_date**: 生效日期，DATE，表示该编制规划版本开始生效的日期。  
* **end\_date**: 结束日期，DATE，表示该编制规划版本失效的日期。当有新的版本生效时，旧版本的end\_date会被更新。  
* **hr\_document\_id**: 人事文件ID，VARCHAR(36)，外键，关联hr\_document表。  
* **approval\_status**: 审批状态，VARCHAR(20)，字典项：草稿、待审批、已批准、已驳回。

#### **3.1.3. 人事文件表 (hr\_document)**

该表用于集中管理各类人事相关文件，包括编制文件。

* **document\_id**: 主键，VARCHAR(36)，系统自动生成。  
* **document\_name**: 文件名称，VARCHAR(200)。  
* **document\_year**: 文件所属年度，INT，例如“2024年编制文件”、“2023年岗位设置文件”。  
* **document\_type**: 文件类型，VARCHAR(50)，字典项：STAFFING\_PLAN (编制文件), POSITION\_SETTING (岗位设置文件), INSTITUTION\_CHANGE (机构变更文件) 等。  
* **document\_url**: 文件存储URL，TEXT，实际文件存储在对象存储服务中。  
* **effective\_date**: 文件生效日期，DATE。  
* **approval\_status**: 审批状态，VARCHAR(20)，字典项：草稿、待审批、已批准、已驳回。

#### **3.1.4. 机构变更记录表 (org\_institution\_change\_log)**

该表记录所有机构的变更历史，支持数据追溯。

* **log\_id**: 主键，VARCHAR(36)，系统自动生成。  
* **institution\_id**: 机构ID，VARCHAR(36)，外键。  
* **change\_type**: 变更类型，VARCHAR(50)，字典项：设立、修改、划转、撤销、合并。  
* **change\_details\_json**: 变更详情，TEXT，JSON格式，存储变更前后的具体数据对比，例如：{"fieldName": "机构名称", "oldValue": "旧名称", "newValue": "新名称", "operator": "操作人姓名"}。  
* **change\_time**: 变更发生时间，DATETIME。  
* **operator\_id**: 操作人ID，VARCHAR(36)，外键。  
* **approval\_doc\_number**: 批文号，VARCHAR(100)。  
* **hr\_document\_id**: 人事文件ID，VARCHAR(36)，外键，关联hr\_document表，用于关联该变更所依据的人事文件。

#### **3.1.5. 岗位序列表 (org\_position\_sequence)**

* **sequence\_id**: 主键，VARCHAR(36)，系统自动生成。  
* **sequence\_name**: 序列名称，VARCHAR(100)，如“管理岗”、“专技岗”。  
* **sequence\_code**: 序列编码，VARCHAR(50)，唯一索引。  
* **description**: 序列描述，TEXT。  
* **status**: 状态，VARCHAR(20)，字典项：NORMAL、DISABLED。  
* **create\_time**: 创建时间，DATETIME。  
* **update\_time**: 更新时间，DATETIME。

#### **3.1.6. 岗位等级表 (org\_position\_level)**

* **level\_id**: 主键，VARCHAR(36)，系统自动生成。  
* **sequence\_id**: 序列ID，VARCHAR(36)，外键，关联org\_position\_sequence表。  
* **level\_name**: 等级名称，VARCHAR(100)，如“一级”、“二级”。  
* **level\_code**: 等级编码，VARCHAR(50)，唯一索引。  
* **level\_order**: 等级排序，INT，用于显示顺序。  
* **description**: 等级描述，TEXT。  
* **status**: 状态，VARCHAR(20)，字典项：NORMAL、DISABLED。  
* **create\_time**: 创建时间，DATETIME。  
* **update\_time**: 更新时间，DATETIME。

### **3.2. 数据字典**

数据字典提供了文档中使用的关键字段的详细解释和可能的取值范围，以确保数据的一致性。

| 字段名 | 表名 | 数据类型 | 描述 |
| :---- | :---- | :---- | :---- |
| institution\_type | org\_institution | VARCHAR(50) | 机构类型，字典项: COLLEGE (学院), DEPARTMENT (部门), AD\_HOC (非常设机构) |
| status | org\_institution, org\_position\_sequence, org\_position\_level | VARCHAR(20) | 机构/序列/等级状态，字典项: NORMAL (正常), DISABLED (停用), WITHDRAWN (撤销), MERGED (合并) |
| change\_type | org\_institution\_change\_log | VARCHAR(50) | 变更类型，字典项: 设立, 修改, 划转, 撤销, 合并, 停用, 启用 |
| position\_category | org\_staffing\_plan | VARCHAR(50) | 岗位类别，字典项: 管理岗, 专技岗, 工勤岗, 其他 |
| approval\_status | org\_staffing\_plan, hr\_document | VARCHAR(20) | 审批状态，字典项: 草稿, 待审批, 已批准, 已驳回 |
| document\_type | hr\_document | VARCHAR(50) | 人事文件类型，字典项: STAFFING\_PLAN (编制文件), POSITION\_SETTING (岗位设置文件), INSTITUTION\_CHANGE (机构变更文件) 等 |
| hr\_document\_id | org\_staffing\_plan, org\_institution\_change\_log | VARCHAR(36) | 关联的人事文件ID |

### **3.3. 历史数据固化考虑**

历史数据固化是本模块设计的核心原则之一，旨在解决因机构组织架构变动而导致历史数据不准确的问题。我们采取了以下几种固化机制：

* **固化机制详解**:  
  1. **solidified\_data\_description (TEXT类型)**：该字段专门用于在org\_institution表中记录机构变更时的历史固化信息。例如，当一个机构被撤销时，该字段可以存储撤销原因、批文号和撤销日期等信息。当机构被合并时，可以记录被合并机构的ID和名称，为历史查询提供清晰的元数据。  
  2. **冗余字段**：在创建rec\_recruitment\_requirement等下游服务关联数据时，department\_name等字段会被冗余存储，以固化当时的名称。这种冗余设计遵循了“数据在写入时固化”的原则，确保历史招聘记录始终指向当时的机构名称，而不是变更后的新名称。  
  3. **机构变更记录**：org\_institution\_change\_log表中change\_details\_json字段以JSON格式存储了变更前后的具体数据对比，为历史追溯提供了详细、结构化的依据。例如，它会记录机构名称从“旧名称”变为“新名称”的完整信息。**此表新增**hr\_document\_id**字段，使得每次组织变更都可以直接追溯到其依据的特定人事文件，增强了变更记录的完整性和可信度。** 4\. **编制规划版本管理**：org\_staffing\_plan表通过effective\_date和end\_date字段实现编制规划的历史版本管理。当编制规划发生变更时（例如核定数量调整），系统会创建一条新的记录，其effective\_date为新规划的生效日期，并更新前一个版本的end\_date。这使得用户可以方便地回溯任何时间点的编制情况。  
* **技术实现与存储策略**:  
  * TEXT类型字段（如reason\_for\_change、solidified\_data\_description、document\_url、change\_details\_json等）在PostgreSQL等现代关系型数据库中会利用TOAST（The Oversized-Attribute Storage Technique）机制进行存储。对于长度超过阈值（通常为2KB）的数据，数据库会自动将其存储在独立的TOAST表中，而在主表中只保留一个指向该TOAST表的指针。这种机制能够有效避免因大文本字段导致的主表行存储效率降低，保证了主表在查询时的性能。  
  * 为了加速查询，org\_institution\_change\_log表会在institution\_id和change\_time上创建组合索引，以便快速查询某个机构的所有历史变更记录。org\_staffing\_plan表会在plan\_year, institution\_id, position\_category, effective\_date上创建组合索引，以支持按时间范围查询特定机构的编制历史。  
* **业务流程与事件通知**:  
  * 在更新机构名称或进行其他变更时，本模块的API仅更新自身的主表数据，不会直接修改下游服务中的冗余字段。  
  * 相反，本模块将通过事件发布机制，向下游服务（如招聘管理服务）发送InstitutionUpdated或InstitutionMerged等事件通知。  
  * 冗余字段的更新由下游服务的事件消费者负责。当它们接收到事件后，会根据事件内容更新自身表中的冗余字段。这种事件驱动的架构实现了系统间的解耦，提高了系统的可维护性和可扩展性。

## **4\. API 接口设计**

### **4.1. HTTP方法使用规范**

* **GET方法**: 用于查询操作，如列表查询、详情查询、导出等。GET请求应是幂等的，不改变服务器状态。  
* **POST方法**: 用于所有修改操作，包括新增、更新、删除、状态变更等。  
* **禁用方法**: 不使用PUT、DELETE、PATCH等HTTP方法，以简化API设计，降低开发和维护复杂度。  
* **参数传递**: 优先使用请求参数(@RequestParam)或请求体(@RequestBody)，避免使用路径参数(@PathVariable)，这有助于API版本的兼容性和灵活性。

### **4.2. 机构管理 API**

所有API接口都将采用统一的响应格式，包含code（业务状态码）、message（中文提示信息）、data（实际业务数据，可选）字段。

* **GET /api/organizations/institutions**: 查询机构列表。  
  * **描述**: 支持层级、状态、类型等筛选条件。返回树状或列表结构。  
  * **请求参数**: parentInstitutionId, status, type等。  
* **GET /api/organizations/institutions/detail**: 查询机构详情。  
  * **描述**: 根据机构ID获取机构详细信息。机构ID通过请求参数传递。  
  * **请求参数**: institutionId。  
* **POST /api/organizations/institutions/create**: 创建机构。  
  * **描述**: 创建新机构，支持常设和非常设机构，信息通过请求体传递。  
  * **请求体**: { "institutionName": "...", "institutionCode": "...", "institutionType": "...", "isAdHoc": false, ... }。  
* **POST /api/organizations/institutions/update**: 更新机构信息。  
  * **描述**: 根据机构ID更新其基本信息。机构ID及更新内容通过请求体传递。  
  * **请求体**: { "institutionId": "...", "institutionName": "新名称", ... }。  
* **POST /api/organizations/institutions/updateProperties**: 更新部门属性。  
  * **描述**: 更新部门的类型、层级、隶属关系、负责人等属性。  
  * **请求体**: { "institutionId": "...", "institutionType": "...", "level": "...", "leaderId": "...", ... }。  
* **POST /api/organizations/institutions/updateStatus**: 修改机构状态。  
  * **描述**: 启用、停用、撤销或合并机构。机构ID和新状态通过请求体传递。  
  * **请求体**: { "institutionId": "...", "status": "DISABLED", "reasonForChange": "...", ... }。  
* **POST /api/organizations/institutions/transfer**: 机构划转。  
  * **描述**: 将机构划转到新的父级机构下，并同步更新员工信息。划转信息通过请求体传递。  
  * **请求体**: { "institutionId": "...", "newParentInstitutionId": "...", "reason": "...", ... }。  
* **POST /api/organizations/institutions/merge**: 机构合并。  
  * **描述**: 将多个机构合并为一个新机构。合并信息通过请求体传递。  
  * **请求体**: { "sourceInstitutionIds": \["...", "..."\], "targetInstitutionId": "...", "mergeReason": "...", ... }。  
* **[规划功能，暂不实现] GET /api/organizations/institutions/export**: 导出机构数据。  
  * **描述**: 导出组织架构数据为指定格式。  
  * **请求参数**: format, filters等。  
* **[规划功能，暂不实现] POST /api/organizations/institutions/import**: 导入机构数据。  
  * **描述**: 导入组织架构数据。  
  * **请求体**: 包含文件上传信息和导入参数。  
* **[规划功能，暂不实现] GET /api/organizations/institutions/diagram**: 获取架构图数据。  
  * **描述**: 获取组织架构图的图形化数据。  
  * **请求参数**: rootInstitutionId, filters等。

### **4.3. 编制管理 API**

* **GET /api/organizations/staffing-plans**: 查询编制规划列表。  
  * **描述**: 支持按年度、机构、岗位类别筛选。  
  * **请求参数**: planYear, institutionId, positionCategory等。  
* **GET /api/organizations/staffing-plans/detail**: 查询编制规划详情。  
  * **描述**: 根据规划ID获取编制规划详情。规划ID通过请求参数传递。  
  * **请求参数**: planId。  
* **POST /api/organizations/staffing-plans/create**: 创建编制规划。  
  * **描述**: 为特定机构和年度创建新的编制规划。规划信息通过请求体传递，并可关联已上传的**人事文件**。  
  * **请求体**: { "planYear": 2026, "institutionId": "...", "positionCategory": "...", "approvedCount": 10, "hrDocumentId": "...", ... }。  
* **POST /api/organizations/staffing-plans/update**: 更新编制信息。  
  * **描述**: 更新现有编制规划。规划ID和更新内容通过请求体传递。  
  * **请求体**: { "planId": "...", "approvedCount": 12, "approvalStatus": "...", "effectiveDate": "...", ... }。  
* **POST /api/organizations/staffing-plans/delete**: 删除编制信息。  
  * **描述**: 删除指定的编制规划记录。规划ID通过请求体传递。  
  * **请求体**: { "planId": "..." }。  
* **[规划功能，暂不实现] GET /api/organizations/staffing-stats**: 查询机构编制统计。  
  * **描述**: 提供在编数、超编数、缺编数等统计数据。

### **4.4. 人事文件管理 API**

* **GET /api/organizations/hr-documents**: 查询人事文件列表。  
  * **描述**: 分页查询所有人事文件，支持按文件名称、年度、类型、审批状态筛选。  
  * **请求参数**: documentName, documentYear, documentType, approvalStatus等。  
* **GET /api/organizations/hr-documents/detail**: 查询人事文件详情。  
  * **描述**: 根据文件ID查询单个编制文件的详细信息。  
  * **请求参数**: documentId。  
* **POST /api/organizations/hr-documents/upload**: 上传人事文件。  
  * **描述**: 上传新的人事文件，并记录其元数据。每次上传视为一份独立的文件。  
  * **请求体**: { "documentName": "...", "documentYear": 2025, "documentType": "...", "documentUrl": "...", "effectiveDate": "...", ... }。  
* **POST /api/organizations/hr-documents/update**: 更新人事文件信息。  
  * **描述**: 更新指定人事文件的元数据，例如文件名称、类型、审批状态等。  
  * **请求体**: { "documentId": "...", "documentName": "新文件名称", "approvalStatus": "APPROVED", ... }。  
* **POST /api/organizations/hr-documents/delete**: 删除人事文件。  
  * **描述**: 逻辑删除指定的人事文件。  
  * **请求体**: { "documentId": "..." }。

### **4.5. 机构变更日志 API**

* **GET /api/organizations/change-logs**: 查询机构变更日志。  
  * **描述**: 支持按机构ID、变更类型和时间范围进行查询。  
  * **请求参数**: institutionId, changeType, startTime, endTime等。

### **4.6. 岗位序列管理 API**

* **GET /api/organizations/positionSequences**: 查询岗位序列列表。  
  * **描述**: 查询所有岗位序列。  
  * **请求参数**: status等。  
* **POST /api/organizations/positionSequences/create**: 创建岗位序列。  
  * **描述**: 创建新的岗位序列。  
  * **请求体**: { "sequenceName": "...", "sequenceCode": "...", ... }。  
* **GET /api/organizations/positionLevels**: 查询岗位等级列表。  
  * **描述**: 查询指定序列下的所有岗位等级。  
  * **请求参数**: sequenceId等。  
* **POST /api/organizations/positionLevels/create**: 创建岗位等级。  
  * **描述**: 为指定序列创建新的岗位等级。  
  * **请求体**: { "sequenceId": "...", "levelName": "...", "levelCode": "...", ... }。