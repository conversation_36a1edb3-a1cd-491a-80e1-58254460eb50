# see https://docs.gitlab.com/ee/ci/yaml/README.html for all available options
variables:
  KUBE_NAMESPACE: "$CI_PROJECT_ROOT_NAMESPACE-test"
  KUBE_DEPLOYMENT_NAME: $CI_PROJECT_NAME
  DOCKER_REPO: "$DOCKER_REGISTRY/hzwangda/$CI_PROJECT_ROOT_NAMESPACE/$CI_PROJECT_NAME:build-$CI_PIPELINE_ID"

workflow:
  rules:
    - if: $CI_PIPELINE_SOURCE == "web" # gitlab网页端手工触发
      when: always
    - if: $CI_COMMIT_REF_NAME == "master"
      when: never
    - if: $CI_COMMIT_REF_NAME == "dev"
      when: never
    - if: $CI_COMMIT_REF_NAME =~ /^prev/
      when: always
    - if: $CI_COMMIT_REF_NAME =~ /^test/
      when: always
    - when: never

stages:
  - build-java
  - build-docker
  - deploy-rancher

build_jar:
  stage: build-java
  variables:
    MAVEN_CLI_OPTS: "-s .m2/settings.xml --batch-mode"
    MAVEN_OPTS: "-Dmaven.repo.local=.m2/repository"
  image: $DOCKER_REGISTRY_PUBLIC/maven:3.6.3-jdk-8
  script:
    - mvn clean package -U
  cache:
    key: mvn-repository-cache
    paths:
      - .m2/repository/
  artifacts:
    paths:
      - target/xxx-service-1.0.0-SNAPSHOT.jar

bulid_image:
  stage: build-docker
  variables:
    DOCKER_HOST: tcp://localhost:2375
    DOCKER_TLS_CERTDIR: ''
  image: $DOCKER_REGISTRY_PUBLIC/docker:stable
  services:
    - name: $DOCKER_REGISTRY_PUBLIC/docker:18.09-dind
      command: ["--registry-mirror=https://$DOCKER_REGISTRY_PUBLIC"]
  script:
    - docker login -u $DOCKER_USER -p $DOCKER_PASSWORD $DOCKER_REGISTRY
    - docker build -t $DOCKER_REPO .
    - docker push $DOCKER_REPO
  after_script:
    - echo "仓库地址 $DOCKER_REPO"
  cache:

deploy_rancher:
  stage: deploy-rancher
  variables:
    GIT_STRATEGY: none # 无需拉git源码
  image: $DOCKER_REGISTRY_PUBLIC/hzwangda/rancher-cli-k8s:2.4.10
  script:
    - rancher login $RACHER_SERVER --token $RACHER_TOKEN --context $RACHER_CONTEXT
    - rancher kubectl set image deployment/$KUBE_DEPLOYMENT_NAME $KUBE_DEPLOYMENT_NAME=$DOCKER_REPO --namespace=$KUBE_NAMESPACE
  after_script:
    - echo "rancher kubectl set image deployment/$KUBE_DEPLOYMENT_NAME $KUBE_DEPLOYMENT_NAME=$DOCKER_REPO --namespace=$KUBE_NAMESPACE"
  cache:
  dependencies: [] # 不需要前面环节的产物artifacts
